'use client';

import { useState, useEffect } from 'react';
import { Bar<PERSON>hart3, TrendingU<PERSON>, Calculator, Target, Building2, Loader } from 'lucide-react';
import PlacementTrendsChart from '../../components/analytics/PlacementTrendsChart';
import SalaryDistributionChart from '../../components/analytics/SalaryDistributionChart';
import ROICalculator from '../../components/analytics/ROICalculator';
import AdmissionPredictor from '../../components/analytics/AdmissionPredictor';
import CareerPath<PERSON>hart from '../../components/analytics/CareerPathChart';
import LocationAnalyticsChart from '../../components/analytics/LocationAnalyticsChart';
import { getAllColleges, getAllCollegeAnalytics } from '../../lib/collegeData';
import { getAnalyticsOverview } from '../../lib/analyticsData';

export default function AnalyticsPage() {
  const [colleges, setColleges] = useState([]);
  const [analyticsData, setAnalyticsData] = useState([]);
  const [overview, setOverview] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Component state
  const [selectedColleges, setSelectedColleges] = useState([]);
  const [selectedCollege, setSelectedCollege] = useState(null);
  const [selectedBranch, setSelectedBranch] = useState('all');
  const [selectedAdmissionCollege, setSelectedAdmissionCollege] = useState(null);

  // Load data on component mount
  useEffect(() => {
    loadAnalyticsData();
  }, []);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load colleges and generate analytics
      const collegesData = await getAllColleges();
      const analytics = await getAllCollegeAnalytics();
      const overviewData = await getAnalyticsOverview(collegesData);

      // Combine colleges with their analytics
      const collegesWithAnalytics = collegesData.map(college => {
        const collegeAnalytics = analytics.find(a => a.id === college.id);
        return {
          ...college,
          analytics: collegeAnalytics
        };
      });

      setColleges(collegesWithAnalytics);
      setAnalyticsData(analytics);
      setOverview(overviewData);

      // Set default selections
      if (collegesWithAnalytics.length > 0) {
        setSelectedCollege(collegesWithAnalytics[0].id);
        setSelectedColleges([collegesWithAnalytics[0].id, collegesWithAnalytics[1]?.id].filter(Boolean));
      }

    } catch (err) {
      console.error('Error loading analytics data:', err);
      setError('Failed to load analytics data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle college selection for comparison
  const handleCollegeSelect = (collegeId) => {
    setSelectedColleges(prev => {
      if (prev.includes(collegeId)) {
        return prev.filter(id => id !== collegeId);
      } else if (prev.length < 5) { // Limit to 5 colleges for comparison
        return [...prev, collegeId];
      }
      return prev;
    });
  };

  // Handle single college selection
  const handleSingleCollegeSelect = (collegeId) => {
    setSelectedCollege(collegeId);
  };

  // Handle admission predictor college selection
  const handleAdmissionCollegeSelect = (collegeAcronym) => {
    setSelectedAdmissionCollege(collegeAcronym);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader className="h-8 w-8 animate-spin text-primary-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading analytics data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
            <h2 className="text-lg font-semibold text-red-800 mb-2">Error Loading Data</h2>
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={loadAnalyticsData}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center space-x-3">
            <div className="bg-primary-100 p-3 rounded-lg">
              <BarChart3 className="h-8 w-8 text-primary-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Advanced Analytics & Insights</h1>
              <p className="text-gray-600 mt-1">
                Comprehensive data analysis for informed college decisions
              </p>
            </div>
          </div>

          {/* Overview Stats */}
          {overview && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-8">
              <div className="bg-primary-50 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Building2 className="h-5 w-5 text-primary-600" />
                  <span className="text-sm font-medium text-primary-700">Total Colleges</span>
                </div>
                <p className="text-2xl font-bold text-primary-900">{overview.totalColleges}</p>
              </div>
              
              <div className="bg-green-50 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-medium text-green-700">Avg Placement</span>
                </div>
                <p className="text-2xl font-bold text-green-900">{overview.averagePlacementRate}%</p>
              </div>
              
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Target className="h-5 w-5 text-blue-600" />
                  <span className="text-sm font-medium text-blue-700">Top Performer</span>
                </div>
                <p className="text-lg font-bold text-blue-900">
                  {overview.topPerformers[0]?.acronym || 'N/A'}
                </p>
                <p className="text-xs text-blue-600">
                  {overview.topPerformers[0]?.placementTrends[overview.topPerformers[0]?.placementTrends.length - 1]?.placementRate}% placement
                </p>
              </div>
              
              <div className="bg-purple-50 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Calculator className="h-5 w-5 text-purple-600" />
                  <span className="text-sm font-medium text-purple-700">Industries</span>
                </div>
                <p className="text-2xl font-bold text-purple-900">
                  {overview.industryDistribution?.length || 0}
                </p>
                <p className="text-xs text-purple-600">sectors covered</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          
          {/* Placement Trends - Full Width */}
          <div className="lg:col-span-12">
            <PlacementTrendsChart
              colleges={colleges}
              selectedColleges={selectedColleges}
              onCollegeSelect={handleCollegeSelect}
              showComparison={true}
            />
          </div>

          {/* Salary Distribution */}
          <div className="lg:col-span-8">
            <SalaryDistributionChart
              colleges={colleges}
              selectedCollege={selectedCollege}
              selectedBranch={selectedBranch}
              onBranchSelect={setSelectedBranch}
            />
          </div>

          {/* Quick Stats */}
          <div className="lg:col-span-4 space-y-6">
            {/* Top Performers */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Performers</h3>
              <div className="space-y-3">
                {overview?.topPerformers?.slice(0, 5).map((college, index) => (
                  <div key={college.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                        index === 0 ? 'bg-yellow-500' :
                        index === 1 ? 'bg-gray-400' :
                        index === 2 ? 'bg-amber-600' : 'bg-gray-300'
                      }`}>
                        {index + 1}
                      </div>
                      <span className="font-medium text-gray-900">{college.acronym}</span>
                    </div>
                    <span className="text-sm font-medium text-green-600">
                      {college.placementTrends[college.placementTrends.length - 1]?.placementRate}%
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Industry Distribution */}
            {overview?.industryDistribution && (
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Industry Distribution</h3>
                <div className="space-y-3">
                  {overview.industryDistribution.slice(0, 5).map((industry, index) => (
                    <div key={industry.industry} className="flex items-center justify-between">
                      <span className="text-sm text-gray-700">{industry.industry}</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-primary-600 h-2 rounded-full" 
                            style={{ width: `${industry.percentage}%` }}
                          ></div>
                        </div>
                        <span className="text-xs font-medium text-gray-600 w-8">
                          {industry.percentage}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* ROI Calculator - Full Width */}
          <div className="lg:col-span-12">
            <ROICalculator
              colleges={colleges}
              selectedCollege={selectedCollege}
              onCollegeSelect={handleSingleCollegeSelect}
            />
          </div>

          {/* Admission Predictor - Full Width */}
          <div className="lg:col-span-12">
            <AdmissionPredictor
              colleges={colleges}
              selectedCollege={selectedAdmissionCollege}
              onCollegeSelect={handleAdmissionCollegeSelect}
            />
          </div>

          {/* Career Path Visualization - Full Width */}
          <div className="lg:col-span-12">
            <CareerPathChart
              colleges={colleges}
              selectedCollege={selectedCollege}
              onCollegeSelect={handleSingleCollegeSelect}
            />
          </div>

          {/* Location Analytics - Full Width */}
          <div className="lg:col-span-12">
            <LocationAnalyticsChart
              colleges={colleges}
              selectedMetric="placement"
              onCollegeSelect={handleSingleCollegeSelect}
            />
          </div>

        </div>
      </div>
    </div>
  );
}
