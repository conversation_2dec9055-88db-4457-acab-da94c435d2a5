'use client';

import { useState, useEffect } from 'react';
import { MapPin, TrendingUp, Users, Building2, Train, Filter } from 'lucide-react';
import OpenStreetMapIntegration from '../mobile/OpenStreetMapIntegration';
import AnalyticsCard from './AnalyticsCard';
import { parseCollegeCoordinates } from '../../lib/geoService';

export default function LocationAnalyticsChart({ 
  colleges = [], 
  selectedMetric = 'placement',
  onCollegeSelect 
}) {
  const [filteredColleges, setFilteredColleges] = useState(colleges);
  const [selectedArea, setSelectedArea] = useState('all');
  const [showMap, setShowMap] = useState(true);
  const [locationStats, setLocationStats] = useState({});

  // Bangalore areas for filtering
  const bangaloreAreas = [
    { value: 'all', label: 'All Areas' },
    { value: 'north', label: 'North Bangalore', bounds: { north: 13.2, south: 13.0 } },
    { value: 'south', label: 'South Bangalore', bounds: { north: 13.0, south: 12.8 } },
    { value: 'east', label: 'East Bangalore', bounds: { east: 77.8, west: 77.6 } },
    { value: 'west', label: 'West Bangalore', bounds: { east: 77.6, west: 77.3 } },
    { value: 'central', label: 'Central Bangalore', bounds: { north: 13.0, south: 12.9, east: 77.6, west: 77.5 } }
  ];

  // Metrics for visualization
  const metrics = [
    { value: 'placement', label: 'Placement Rate', icon: TrendingUp, color: '#10b981' },
    { value: 'students', label: 'Student Capacity', icon: Users, color: '#3b82f6' },
    { value: 'campus', label: 'Campus Size', icon: Building2, color: '#8b5cf6' },
    { value: 'metro', label: 'Metro Access', icon: Train, color: '#f59e0b' }
  ];

  useEffect(() => {
    filterCollegesByArea();
    calculateLocationStats();
  }, [colleges, selectedArea, selectedMetric]);

  const filterCollegesByArea = () => {
    if (selectedArea === 'all') {
      setFilteredColleges(colleges);
      return;
    }

    const area = bangaloreAreas.find(a => a.value === selectedArea);
    if (!area?.bounds) {
      setFilteredColleges(colleges);
      return;
    }

    const filtered = colleges.filter(college => {
      const coords = parseCollegeCoordinates(college);
      const lat = coords.lat;
      const lng = coords.lng;

      const { bounds } = area;
      let inBounds = true;

      if (bounds.north && bounds.south) {
        inBounds = inBounds && lat >= bounds.south && lat <= bounds.north;
      }
      if (bounds.east && bounds.west) {
        inBounds = inBounds && lng >= bounds.west && lng <= bounds.east;
      }

      return inBounds;
    });

    setFilteredColleges(filtered);
  };

  const calculateLocationStats = () => {
    const stats = {};

    bangaloreAreas.forEach(area => {
      if (area.value === 'all') return;

      const areaColleges = colleges.filter(college => {
        if (!area.bounds) return false;

        const coords = parseCollegeCoordinates(college);
        const lat = coords.lat;
        const lng = coords.lng;

        const { bounds } = area;
        let inBounds = true;

        if (bounds.north && bounds.south) {
          inBounds = inBounds && lat >= bounds.south && lat <= bounds.north;
        }
        if (bounds.east && bounds.west) {
          inBounds = inBounds && lng >= bounds.west && lng <= bounds.east;
        }

        return inBounds;
      });

      stats[area.value] = {
        count: areaColleges.length,
        avgPlacement: areaColleges.length > 0 
          ? (areaColleges.reduce((sum, c) => sum + (c.placementRate || 0), 0) / areaColleges.length).toFixed(1)
          : 0,
        metroAccess: areaColleges.filter(c => c.metroAccess).length,
        topRanked: areaColleges.filter(c => c.ranking <= 10).length
      };
    });

    setLocationStats(stats);
  };

  const getCollegeMarkerData = () => {
    return filteredColleges.map(college => ({
      ...college,
      ...parseCollegeCoordinates(college),
      rank: college.ranking,
      placementRate: college.placementRate || 0,
      // Add metric-specific data for marker sizing/coloring
      metricValue: selectedMetric === 'placement' ? college.placementRate :
                   selectedMetric === 'students' ? college.totalStudents :
                   selectedMetric === 'campus' ? parseFloat(college.campusSize) :
                   selectedMetric === 'metro' ? (college.metroAccess ? 100 : 0) : 0
    }));
  };

  const selectedMetricData = metrics.find(m => m.value === selectedMetric);

  return (
    <AnalyticsCard
      title="Location-Based Analytics"
      subtitle="Analyze college distribution and metrics across Bangalore"
      icon={MapPin}
    >
      <div className="space-y-6">
        {/* Controls */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Area Filter
            </label>
            <select
              value={selectedArea}
              onChange={(e) => setSelectedArea(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              {bangaloreAreas.map(area => (
                <option key={area.value} value={area.value}>
                  {area.label}
                </option>
              ))}
            </select>
          </div>

          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Metric
            </label>
            <select
              value={selectedMetric}
              onChange={(e) => onCollegeSelect?.(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              {metrics.map(metric => (
                <option key={metric.value} value={metric.value}>
                  {metric.label}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={() => setShowMap(!showMap)}
              className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors duration-200 flex items-center space-x-2"
            >
              <MapPin className="w-4 h-4" />
              <span>{showMap ? 'Hide' : 'Show'} Map</span>
            </button>
          </div>
        </div>

        {/* Area Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Object.entries(locationStats).map(([area, stats]) => {
            const areaInfo = bangaloreAreas.find(a => a.value === area);
            return (
              <div
                key={area}
                className={`p-4 rounded-lg border-2 transition-all duration-200 cursor-pointer ${
                  selectedArea === area
                    ? 'border-primary-500 bg-primary-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedArea(area)}
              >
                <h4 className="font-medium text-gray-900 text-sm mb-2">
                  {areaInfo?.label}
                </h4>
                <div className="space-y-1 text-xs text-gray-600">
                  <div>Colleges: {stats.count}</div>
                  <div>Avg Placement: {stats.avgPlacement}%</div>
                  <div>Metro Access: {stats.metroAccess}</div>
                  <div>Top 10: {stats.topRanked}</div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Map Visualization */}
        {showMap && (
          <div className="bg-gray-50 rounded-lg overflow-hidden">
            <div className="p-4 bg-white border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {selectedMetricData && (
                    <>
                      <selectedMetricData.icon 
                        className="w-5 h-5" 
                        style={{ color: selectedMetricData.color }} 
                      />
                      <span className="font-medium text-gray-900">
                        {selectedMetricData.label} Distribution
                      </span>
                    </>
                  )}
                </div>
                <div className="text-sm text-gray-600">
                  {filteredColleges.length} colleges in {bangaloreAreas.find(a => a.value === selectedArea)?.label}
                </div>
              </div>
            </div>
            
            <OpenStreetMapIntegration
              colleges={getCollegeMarkerData()}
              onCollegeSelect={onCollegeSelect}
              showRoute={false}
              className="h-96"
            />
          </div>
        )}

        {/* Summary Statistics */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6">
          <h4 className="font-semibold text-gray-900 mb-4">Location Insights</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <div className="text-gray-600">Total Colleges</div>
              <div className="text-2xl font-bold text-gray-900">{filteredColleges.length}</div>
            </div>
            <div>
              <div className="text-gray-600">Average Placement Rate</div>
              <div className="text-2xl font-bold text-green-600">
                {filteredColleges.length > 0 
                  ? (filteredColleges.reduce((sum, c) => sum + (c.placementRate || 0), 0) / filteredColleges.length).toFixed(1)
                  : 0}%
              </div>
            </div>
            <div>
              <div className="text-gray-600">Metro Accessible</div>
              <div className="text-2xl font-bold text-blue-600">
                {filteredColleges.filter(c => c.metroAccess).length}
              </div>
            </div>
          </div>
        </div>
      </div>
    </AnalyticsCard>
  );
}
