/**
 * Notification Scheduler
 * Handles automated scheduling of notifications for deadlines, reminders, and updates
 */

import { collection, addDoc, query, where, getDocs, updateDoc, doc, serverTimestamp } from 'firebase/firestore';
import { db } from './firebase';
import { NOTIFICATION_TYPES } from './pushNotifications';

// College application deadlines (mock data - would come from database)
const COLLEGE_DEADLINES = [
  {
    collegeId: 'rvce',
    collegeName: 'R.V. College of Engineering',
    deadline: new Date('2024-06-15'),
    type: 'application',
    reminderDays: [30, 7, 3, 1]
  },
  {
    collegeId: 'msrit',
    collegeName: 'M.S. Ramaiah Institute of Technology',
    deadline: new Date('2024-06-20'),
    type: 'application',
    reminderDays: [30, 7, 3, 1]
  },
  {
    collegeId: 'pes',
    collegeName: 'PES University',
    deadline: new Date('2024-06-10'),
    type: 'application',
    reminderDays: [30, 7, 3, 1]
  }
];

class NotificationScheduler {
  constructor() {
    this.isInitialized = false;
    this.scheduledJobs = new Map();
  }

  // Initialize the scheduler
  async initialize() {
    if (this.isInitialized) return;
    
    try {
      console.log('[NotificationScheduler] Initializing...');
      
      // Schedule deadline reminders
      await this.scheduleDeadlineReminders();
      
      // Schedule weekly placement updates
      await this.scheduleWeeklyUpdates();
      
      // Schedule personalized reminders
      await this.schedulePersonalizedReminders();
      
      this.isInitialized = true;
      console.log('[NotificationScheduler] Initialized successfully');
    } catch (error) {
      console.error('[NotificationScheduler] Initialization failed:', error);
    }
  }

  // Schedule deadline reminders for all colleges
  async scheduleDeadlineReminders() {
    for (const college of COLLEGE_DEADLINES) {
      for (const reminderDays of college.reminderDays) {
        const reminderDate = new Date(college.deadline);
        reminderDate.setDate(reminderDate.getDate() - reminderDays);
        
        // Only schedule future reminders
        if (reminderDate > new Date()) {
          await this.scheduleNotification({
            type: NOTIFICATION_TYPES.APPLICATION_DEADLINE,
            title: `Application Deadline Reminder`,
            body: `${college.collegeName} application deadline is in ${reminderDays} day${reminderDays > 1 ? 's' : ''}`,
            scheduledTime: reminderDate,
            data: {
              collegeId: college.collegeId,
              collegeName: college.collegeName,
              deadline: college.deadline.toISOString(),
              daysRemaining: reminderDays,
              actionUrl: `/colleges/${college.collegeId}`
            },
            targetAudience: 'interested_users', // Users who favorited or viewed this college
            priority: reminderDays <= 3 ? 'high' : 'normal'
          });
        }
      }
    }
  }

  // Schedule weekly placement updates
  async scheduleWeeklyUpdates() {
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);
    nextWeek.setHours(10, 0, 0, 0); // 10 AM every week
    
    await this.scheduleNotification({
      type: NOTIFICATION_TYPES.PLACEMENT_UPDATE,
      title: 'Weekly Placement Report',
      body: 'Check out the latest placement statistics and company updates',
      scheduledTime: nextWeek,
      data: {
        actionUrl: '/analytics',
        reportType: 'weekly_placement'
      },
      targetAudience: 'all_users',
      recurring: {
        frequency: 'weekly',
        dayOfWeek: nextWeek.getDay()
      }
    });
  }

  // Schedule personalized reminders based on user activity
  async schedulePersonalizedReminders() {
    try {
      // Get users who haven't been active recently
      const inactiveUsersQuery = query(
        collection(db, 'users'),
        where('lastActive', '<', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)) // 7 days ago
      );
      
      const inactiveUsers = await getDocs(inactiveUsersQuery);
      
      inactiveUsers.forEach(async (userDoc) => {
        const userData = userDoc.data();
        const userId = userDoc.id;
        
        // Schedule re-engagement notification
        const reminderTime = new Date();
        reminderTime.setHours(reminderTime.getHours() + 24); // Tomorrow
        
        await this.scheduleNotification({
          type: NOTIFICATION_TYPES.PERSONALIZED_REMINDER,
          title: 'Don\'t miss out on college updates!',
          body: 'New colleges and placement data have been added. Check them out!',
          scheduledTime: reminderTime,
          data: {
            userId,
            actionUrl: '/colleges',
            reminderType: 'reengagement'
          },
          targetUsers: [userId]
        });
      });
    } catch (error) {
      console.error('[NotificationScheduler] Error scheduling personalized reminders:', error);
    }
  }

  // Schedule a notification
  async scheduleNotification(notificationData) {
    try {
      const scheduledNotificationRef = collection(db, 'scheduledNotifications');
      const docRef = await addDoc(scheduledNotificationRef, {
        ...notificationData,
        status: 'scheduled',
        createdAt: serverTimestamp(),
        attempts: 0,
        maxAttempts: 3
      });
      
      console.log('[NotificationScheduler] Notification scheduled:', docRef.id);
      
      // Set up local timer if the notification is due soon (within 24 hours)
      const timeUntilNotification = notificationData.scheduledTime.getTime() - Date.now();
      if (timeUntilNotification > 0 && timeUntilNotification <= 24 * 60 * 60 * 1000) {
        this.setLocalTimer(docRef.id, timeUntilNotification, notificationData);
      }
      
      return docRef.id;
    } catch (error) {
      console.error('[NotificationScheduler] Error scheduling notification:', error);
      throw error;
    }
  }

  // Set local timer for immediate notifications
  setLocalTimer(notificationId, delay, notificationData) {
    const timerId = setTimeout(async () => {
      try {
        await this.processNotification(notificationId, notificationData);
      } catch (error) {
        console.error('[NotificationScheduler] Error processing timed notification:', error);
      }
    }, delay);
    
    this.scheduledJobs.set(notificationId, timerId);
  }

  // Process and send notification
  async processNotification(notificationId, notificationData) {
    try {
      console.log('[NotificationScheduler] Processing notification:', notificationId);
      
      // Get target users
      const targetUsers = await this.getTargetUsers(notificationData);
      
      if (targetUsers.length === 0) {
        console.log('[NotificationScheduler] No target users found for notification:', notificationId);
        return;
      }
      
      // Send notification to each user
      const sendPromises = targetUsers.map(userId => 
        this.sendNotificationToUser(userId, notificationData)
      );
      
      const results = await Promise.allSettled(sendPromises);
      
      // Count successful sends
      const successCount = results.filter(result => result.status === 'fulfilled').length;
      const failureCount = results.length - successCount;
      
      // Update notification status
      const notificationRef = doc(db, 'scheduledNotifications', notificationId);
      await updateDoc(notificationRef, {
        status: 'sent',
        sentAt: serverTimestamp(),
        targetUserCount: targetUsers.length,
        successCount,
        failureCount,
        lastProcessed: serverTimestamp()
      });
      
      console.log(`[NotificationScheduler] Notification sent to ${successCount}/${targetUsers.length} users`);
      
      // Schedule recurring notification if needed
      if (notificationData.recurring) {
        await this.scheduleRecurringNotification(notificationData);
      }
      
    } catch (error) {
      console.error('[NotificationScheduler] Error processing notification:', error);
      
      // Update notification with error status
      try {
        const notificationRef = doc(db, 'scheduledNotifications', notificationId);
        await updateDoc(notificationRef, {
          status: 'failed',
          error: error.message,
          lastProcessed: serverTimestamp()
        });
      } catch (updateError) {
        console.error('[NotificationScheduler] Error updating notification status:', updateError);
      }
    }
  }

  // Get target users based on audience criteria
  async getTargetUsers(notificationData) {
    const { targetAudience, targetUsers, data } = notificationData;
    
    if (targetUsers && targetUsers.length > 0) {
      return targetUsers;
    }
    
    try {
      let usersQuery;
      
      switch (targetAudience) {
        case 'all_users':
          usersQuery = query(
            collection(db, 'users'),
            where('notificationsEnabled', '==', true)
          );
          break;
          
        case 'interested_users':
          // Users who have favorited or viewed the specific college
          if (data.collegeId) {
            usersQuery = query(
              collection(db, 'users'),
              where('notificationsEnabled', '==', true),
              where('favorites', 'array-contains', data.collegeId)
            );
          }
          break;
          
        case 'active_users':
          // Users active in the last 30 days
          const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
          usersQuery = query(
            collection(db, 'users'),
            where('notificationsEnabled', '==', true),
            where('lastActive', '>', thirtyDaysAgo)
          );
          break;
          
        default:
          return [];
      }
      
      const querySnapshot = await getDocs(usersQuery);
      return querySnapshot.docs.map(doc => doc.id);
      
    } catch (error) {
      console.error('[NotificationScheduler] Error getting target users:', error);
      return [];
    }
  }

  // Send notification to a specific user
  async sendNotificationToUser(userId, notificationData) {
    try {
      // This would integrate with your push notification service
      // For now, we'll just log and store in the database
      
      const userNotificationRef = collection(db, 'userNotifications');
      await addDoc(userNotificationRef, {
        userId,
        type: notificationData.type,
        title: notificationData.title,
        body: notificationData.body,
        data: notificationData.data,
        status: 'sent',
        sentAt: serverTimestamp(),
        read: false
      });
      
      console.log(`[NotificationScheduler] Notification sent to user: ${userId}`);
      return true;
    } catch (error) {
      console.error(`[NotificationScheduler] Error sending notification to user ${userId}:`, error);
      throw error;
    }
  }

  // Schedule recurring notification
  async scheduleRecurringNotification(originalNotification) {
    const { recurring, scheduledTime } = originalNotification;
    
    if (!recurring) return;
    
    const nextScheduledTime = new Date(scheduledTime);
    
    switch (recurring.frequency) {
      case 'daily':
        nextScheduledTime.setDate(nextScheduledTime.getDate() + 1);
        break;
      case 'weekly':
        nextScheduledTime.setDate(nextScheduledTime.getDate() + 7);
        break;
      case 'monthly':
        nextScheduledTime.setMonth(nextScheduledTime.getMonth() + 1);
        break;
      default:
        return;
    }
    
    // Schedule the next occurrence
    await this.scheduleNotification({
      ...originalNotification,
      scheduledTime: nextScheduledTime
    });
  }

  // Cancel scheduled notification
  async cancelNotification(notificationId) {
    try {
      // Cancel local timer if exists
      if (this.scheduledJobs.has(notificationId)) {
        clearTimeout(this.scheduledJobs.get(notificationId));
        this.scheduledJobs.delete(notificationId);
      }
      
      // Update database status
      const notificationRef = doc(db, 'scheduledNotifications', notificationId);
      await updateDoc(notificationRef, {
        status: 'cancelled',
        cancelledAt: serverTimestamp()
      });
      
      console.log('[NotificationScheduler] Notification cancelled:', notificationId);
    } catch (error) {
      console.error('[NotificationScheduler] Error cancelling notification:', error);
      throw error;
    }
  }

  // Get scheduled notifications for a user
  async getUserScheduledNotifications(userId) {
    try {
      const notificationsQuery = query(
        collection(db, 'userNotifications'),
        where('userId', '==', userId),
        where('status', '==', 'sent')
      );
      
      const querySnapshot = await getDocs(notificationsQuery);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('[NotificationScheduler] Error getting user notifications:', error);
      return [];
    }
  }
}

// Create singleton instance
const notificationScheduler = new NotificationScheduler();

export default notificationScheduler;
