'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, <PERSON>atter<PERSON><PERSON>, <PERSON>atter, Cell } from 'recharts';
import { DollarSign, Filter, Users, TrendingUp } from 'lucide-react';
import AnalyticsCard from './AnalyticsCard';

const SalaryDistributionChart = ({ 
  colleges = [], 
  selectedCollege = null,
  selectedBranch = 'all',
  onBranchSelect = () => {},
  showBoxPlot = false 
}) => {
  const [chartType, setChartType] = useState('bar');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Process salary distribution data
  const processSalaryData = () => {
    if (!colleges.length) return [];

    const college = selectedCollege ? 
      colleges.find(c => c.id === selectedCollege) : 
      colleges[0];

    if (!college) return [];

    const analytics = college.analytics || college;
    let salaryData = analytics.salaryDistribution || [];

    // Filter by branch if selected
    if (selectedBranch !== 'all') {
      salaryData = salaryData.filter(branch => 
        branch.branch.toLowerCase().includes(selectedBranch.toLowerCase())
      );
    }

    return salaryData.map(branch => ({
      ...branch,
      range: branch.max - branch.min,
      iqr: branch.q3 - branch.q1
    }));
  };

  // Process data for branch comparison across colleges
  const processBranchComparison = () => {
    if (!colleges.length || selectedBranch === 'all') return [];

    return colleges.slice(0, 8).map(college => {
      const analytics = college.analytics || college;
      const branchData = analytics.salaryDistribution?.find(branch => 
        branch.branch.toLowerCase().includes(selectedBranch.toLowerCase())
      );

      return {
        college: college.acronym,
        fullName: college.name,
        min: branchData?.min || 0,
        q1: branchData?.q1 || 0,
        median: branchData?.median || 0,
        q3: branchData?.q3 || 0,
        max: branchData?.max || 0,
        average: branchData?.average || 0,
        count: branchData?.count || 0
      };
    }).filter(data => data.average > 0);
  };

  // Calculate salary statistics
  const calculateSalaryStats = () => {
    const salaryData = processSalaryData();
    if (!salaryData.length) return null;

    const totalStudents = salaryData.reduce((sum, branch) => sum + branch.count, 0);
    const weightedAverage = salaryData.reduce((sum, branch) => 
      sum + (branch.average * branch.count), 0) / totalStudents;
    
    const highestPaying = salaryData.reduce((max, branch) => 
      branch.average > max.average ? branch : max, salaryData[0]);
    
    const mostPopular = salaryData.reduce((max, branch) => 
      branch.count > max.count ? branch : max, salaryData[0]);

    return {
      totalStudents,
      weightedAverage: Math.round(weightedAverage * 10) / 10,
      highestPaying,
      mostPopular,
      branches: salaryData.length
    };
  };

  const salaryData = processSalaryData();
  const branchComparison = processBranchComparison();
  const stats = calculateSalaryStats();

  // Custom tooltip for salary charts
  const SalaryTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-900 mb-2">{label}</p>
          <div className="space-y-1 text-sm">
            <p>Min: <span className="font-medium">₹{data.min} LPA</span></p>
            <p>Q1: <span className="font-medium">₹{data.q1} LPA</span></p>
            <p>Median: <span className="font-medium">₹{data.median} LPA</span></p>
            <p>Q3: <span className="font-medium">₹{data.q3} LPA</span></p>
            <p>Max: <span className="font-medium">₹{data.max} LPA</span></p>
            <p>Average: <span className="font-medium">₹{data.average} LPA</span></p>
            <p>Students: <span className="font-medium">{data.count}</span></p>
          </div>
        </div>
      );
    }
    return null;
  };

  // Box plot component (simplified representation using bars)
  const BoxPlotBar = ({ data, x, width, height }) => {
    const yScale = (value) => height - (value / 60) * height; // Assuming max 60 LPA
    
    return (
      <g>
        {/* Min-Max line */}
        <line
          x1={x + width / 2}
          y1={yScale(data.min)}
          x2={x + width / 2}
          y2={yScale(data.max)}
          stroke="#6b7280"
          strokeWidth={2}
        />
        
        {/* Box (Q1-Q3) */}
        <rect
          x={x + width * 0.25}
          y={yScale(data.q3)}
          width={width * 0.5}
          height={yScale(data.q1) - yScale(data.q3)}
          fill="#3b82f6"
          fillOpacity={0.7}
          stroke="#1d4ed8"
          strokeWidth={1}
        />
        
        {/* Median line */}
        <line
          x1={x + width * 0.25}
          y1={yScale(data.median)}
          x2={x + width * 0.75}
          y2={yScale(data.median)}
          stroke="#1d4ed8"
          strokeWidth={3}
        />
        
        {/* Min/Max caps */}
        <line
          x1={x + width * 0.4}
          y1={yScale(data.min)}
          x2={x + width * 0.6}
          y2={yScale(data.min)}
          stroke="#6b7280"
          strokeWidth={2}
        />
        <line
          x1={x + width * 0.4}
          y1={yScale(data.max)}
          x2={x + width * 0.6}
          y2={yScale(data.max)}
          stroke="#6b7280"
          strokeWidth={2}
        />
      </g>
    );
  };

  const branches = ['Computer Science', 'Electronics & Communication', 'Mechanical', 'Civil', 'Electrical', 'Information Science'];

  return (
    <AnalyticsCard
      title="Salary Distribution Analysis"
      subtitle="Branch-wise salary ranges and distribution patterns"
      info="Analyze salary distributions across different engineering branches"
      loading={loading}
      error={error}
      className="col-span-full lg:col-span-2"
    >
      <div className="space-y-6">
        {/* Controls */}
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <select
                value={selectedBranch}
                onChange={(e) => onBranchSelect(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="all">All Branches</option>
                {branches.map(branch => (
                  <option key={branch} value={branch.toLowerCase().replace(/\s+/g, '')}>
                    {branch}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setChartType('bar')}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                chartType === 'bar' 
                  ? 'bg-primary-600 text-white' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Bar Chart
            </button>
            <button
              onClick={() => setChartType('box')}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                chartType === 'box' 
                  ? 'bg-primary-600 text-white' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Box Plot
            </button>
          </div>
        </div>

        {/* Statistics Cards */}
        {stats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-primary-50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <DollarSign className="h-5 w-5 text-primary-600" />
                <span className="text-sm font-medium text-primary-700">Avg Salary</span>
              </div>
              <p className="text-2xl font-bold text-primary-900">₹{stats.weightedAverage}</p>
              <p className="text-xs text-primary-600">LPA</p>
            </div>
            
            <div className="bg-green-50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
                <span className="text-sm font-medium text-green-700">Highest Branch</span>
              </div>
              <p className="text-lg font-bold text-green-900">{stats.highestPaying.branch.split(' ')[0]}</p>
              <p className="text-xs text-green-600">₹{stats.highestPaying.average} LPA</p>
            </div>
            
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Users className="h-5 w-5 text-blue-600" />
                <span className="text-sm font-medium text-blue-700">Most Popular</span>
              </div>
              <p className="text-lg font-bold text-blue-900">{stats.mostPopular.branch.split(' ')[0]}</p>
              <p className="text-xs text-blue-600">{stats.mostPopular.count} students</p>
            </div>
            
            <div className="bg-purple-50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Filter className="h-5 w-5 text-purple-600" />
                <span className="text-sm font-medium text-purple-700">Total Students</span>
              </div>
              <p className="text-2xl font-bold text-purple-900">{stats.totalStudents}</p>
              <p className="text-xs text-purple-600">{stats.branches} branches</p>
            </div>
          </div>
        )}

        {/* Chart */}
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            {selectedBranch !== 'all' && branchComparison.length > 0 ? (
              // College comparison for specific branch
              <BarChart data={branchComparison}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis 
                  dataKey="college" 
                  stroke="#6b7280"
                  fontSize={12}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis 
                  stroke="#6b7280"
                  fontSize={12}
                  label={{ value: 'Salary (LPA)', angle: -90, position: 'insideLeft' }}
                />
                <Tooltip content={<SalaryTooltip />} />
                <Legend />
                <Bar dataKey="min" fill="#fca5a5" name="Min" />
                <Bar dataKey="average" fill="#3b82f6" name="Average" />
                <Bar dataKey="max" fill="#10b981" name="Max" />
              </BarChart>
            ) : (
              // Branch-wise distribution for single college
              <BarChart data={salaryData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis 
                  dataKey="branch" 
                  stroke="#6b7280"
                  fontSize={12}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis 
                  stroke="#6b7280"
                  fontSize={12}
                  label={{ value: 'Salary (LPA)', angle: -90, position: 'insideLeft' }}
                />
                <Tooltip content={<SalaryTooltip />} />
                <Legend />
                <Bar dataKey="min" fill="#fca5a5" name="Min" />
                <Bar dataKey="median" fill="#60a5fa" name="Median" />
                <Bar dataKey="average" fill="#3b82f6" name="Average" />
                <Bar dataKey="max" fill="#10b981" name="Max" />
              </BarChart>
            )}
          </ResponsiveContainer>
        </div>

        {/* Branch Details */}
        {salaryData.length > 0 && selectedBranch === 'all' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {salaryData.slice(0, 6).map((branch, index) => (
              <div key={branch.branch} className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-3">{branch.branch}</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="text-gray-600">Range:</span>
                    <p className="font-medium">₹{branch.min} - ₹{branch.max} LPA</p>
                  </div>
                  <div>
                    <span className="text-gray-600">Median:</span>
                    <p className="font-medium">₹{branch.median} LPA</p>
                  </div>
                  <div>
                    <span className="text-gray-600">Average:</span>
                    <p className="font-medium">₹{branch.average} LPA</p>
                  </div>
                  <div>
                    <span className="text-gray-600">Students:</span>
                    <p className="font-medium">{branch.count}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </AnalyticsCard>
  );
};

export default SalaryDistributionChart;
