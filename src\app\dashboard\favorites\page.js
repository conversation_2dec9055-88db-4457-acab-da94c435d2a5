'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  Heart, 
  Search, 
  Filter, 
  Grid, 
  List, 
  ArrowUpDown,
  MapPin,
  Award,
  Users,
  DollarSign,
  Trash2,
  ExternalLink
} from 'lucide-react';
import { useFavorites } from '../../../hooks/useFavorites';
import { getCollegeById } from '../../../lib/collegeData';
import CollegeCard from '../../../components/CollegeCard';

const FavoritesPage = () => {
  const { favorites, loading, removeFavorite } = useFavorites();
  const [favoriteColleges, setFavoriteColleges] = useState([]);
  const [filteredColleges, setFilteredColleges] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('dateAdded');
  const [viewMode, setViewMode] = useState('grid');
  const [loadingColleges, setLoadingColleges] = useState(true);

  // Load college details for favorites
  useEffect(() => {
    const loadFavoriteColleges = async () => {
      if (favorites.length === 0) {
        setFavoriteColleges([]);
        setFilteredColleges([]);
        setLoadingColleges(false);
        return;
      }

      try {
        setLoadingColleges(true);
        const collegePromises = favorites.map(async (favorite) => {
          const college = await getCollegeById(favorite.collegeId);
          return college ? { ...college, favoriteDate: favorite.createdAt } : null;
        });

        const colleges = await Promise.all(collegePromises);
        const validColleges = colleges.filter(Boolean);
        setFavoriteColleges(validColleges);
        setFilteredColleges(validColleges);
      } catch (error) {
        console.error('Error loading favorite colleges:', error);
      } finally {
        setLoadingColleges(false);
      }
    };

    loadFavoriteColleges();
  }, [favorites]);

  // Filter and search colleges
  useEffect(() => {
    let filtered = [...favoriteColleges];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(college =>
        college.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        college.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
        college.courses.some(course => 
          course.toLowerCase().includes(searchQuery.toLowerCase())
        )
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'ranking':
          return a.ranking - b.ranking;
        case 'fees':
          return a.fees.tuition - b.fees.tuition;
        case 'placement':
          return b.placements.averagePackage - a.placements.averagePackage;
        case 'dateAdded':
        default:
          return new Date(b.favoriteDate) - new Date(a.favoriteDate);
      }
    });

    setFilteredColleges(filtered);
  }, [favoriteColleges, searchQuery, sortBy]);

  const handleRemoveFavorite = async (collegeId) => {
    if (window.confirm('Are you sure you want to remove this college from your favorites?')) {
      await removeFavorite(collegeId);
    }
  };

  const sortOptions = [
    { value: 'dateAdded', label: 'Date Added' },
    { value: 'name', label: 'Name' },
    { value: 'ranking', label: 'Ranking' },
    { value: 'fees', label: 'Fees' },
    { value: 'placement', label: 'Placement Package' }
  ];

  if (loading || loadingColleges) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="h-8 bg-gray-200 rounded w-48 animate-pulse"></div>
          <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-white rounded-xl shadow-sm p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-2/3"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
            <Heart className="w-6 h-6 text-red-600" />
            <span>My Favorites</span>
          </h1>
          <p className="text-gray-600 mt-1">
            {favoriteColleges.length} college{favoriteColleges.length !== 1 ? 's' : ''} saved
          </p>
        </div>

        {favoriteColleges.length > 0 && (
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
              className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              {viewMode === 'grid' ? <List className="w-4 h-4" /> : <Grid className="w-4 h-4" />}
            </button>
          </div>
        )}
      </div>

      {favoriteColleges.length === 0 ? (
        <div className="text-center py-12">
          <Heart className="w-16 h-16 text-gray-300 mx-auto mb-6" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">No favorites yet</h3>
          <p className="text-gray-600 mb-6 max-w-md mx-auto">
            Start exploring colleges and save your favorites to keep track of the ones you're interested in.
          </p>
          <Link
            href="/colleges"
            className="btn-primary"
          >
            Explore Colleges
          </Link>
        </div>
      ) : (
        <>
          {/* Filters and Search */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search your favorites..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              {/* Sort */}
              <div className="flex items-center space-x-2">
                <ArrowUpDown className="w-4 h-4 text-gray-400" />
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  {sortOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      Sort by {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Results */}
          {filteredColleges.length === 0 ? (
            <div className="text-center py-8">
              <Search className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">No colleges match your search criteria</p>
            </div>
          ) : (
            <div className={viewMode === 'grid' 
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
              : 'space-y-4'
            }>
              {filteredColleges.map((college) => (
                <div key={college.id} className="relative group">
                  {viewMode === 'grid' ? (
                    <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow">
                      <CollegeCard college={college} />
                      <div className="absolute top-4 right-4">
                        <button
                          onClick={() => handleRemoveFavorite(college.id)}
                          className="p-2 bg-white rounded-full shadow-md hover:bg-red-50 hover:text-red-600 transition-colors opacity-0 group-hover:opacity-100"
                          title="Remove from favorites"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-white rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-start justify-between mb-4">
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                                <Link 
                                  href={`/colleges/${college.id}`}
                                  className="hover:text-primary-600 transition-colors"
                                >
                                  {college.name}
                                </Link>
                              </h3>
                              <div className="flex items-center space-x-4 text-sm text-gray-500">
                                <div className="flex items-center space-x-1">
                                  <MapPin className="w-3 h-3" />
                                  <span>{college.location}</span>
                                </div>
                                <div className="flex items-center space-x-1">
                                  <Award className="w-3 h-3" />
                                  <span>Rank #{college.ranking}</span>
                                </div>
                              </div>
                            </div>
                            <button
                              onClick={() => handleRemoveFavorite(college.id)}
                              className="p-2 hover:bg-red-50 hover:text-red-600 rounded-full transition-colors"
                              title="Remove from favorites"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>

                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                            <div className="text-center">
                              <div className="text-lg font-semibold text-gray-900">
                                {college.placements.percentage}%
                              </div>
                              <div className="text-xs text-gray-500">Placement Rate</div>
                            </div>
                            <div className="text-center">
                              <div className="text-lg font-semibold text-gray-900">
                                ₹{(college.placements.averagePackage / 100000).toFixed(1)}L
                              </div>
                              <div className="text-xs text-gray-500">Avg Package</div>
                            </div>
                            <div className="text-center">
                              <div className="text-lg font-semibold text-gray-900">
                                ₹{(college.fees.tuition / 100000).toFixed(1)}L
                              </div>
                              <div className="text-xs text-gray-500">Annual Fees</div>
                            </div>
                            <div className="text-center">
                              <div className="text-lg font-semibold text-gray-900">
                                {college.studentCount}
                              </div>
                              <div className="text-xs text-gray-500">Students</div>
                            </div>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="text-xs text-gray-500">
                              Added on {new Date(college.favoriteDate).toLocaleDateString()}
                            </div>
                            <Link
                              href={`/colleges/${college.id}`}
                              className="flex items-center space-x-1 text-primary-600 hover:text-primary-700 text-sm font-medium"
                            >
                              <span>View Details</span>
                              <ExternalLink className="w-3 h-3" />
                            </Link>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default FavoritesPage;
