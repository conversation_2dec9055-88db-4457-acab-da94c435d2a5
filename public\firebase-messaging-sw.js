// Firebase Messaging Service Worker
// Handles background push notifications

importScripts('https://www.gstatic.com/firebasejs/10.14.1/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.14.1/firebase-messaging-compat.js');

// Firebase configuration
const firebaseConfig = {
  apiKey: "demo-key",
  authDomain: "demo-project.firebaseapp.com",
  projectId: "demo-project",
  storageBucket: "demo-project.appspot.com",
  messagingSenderId: "123456789",
  appId: "1:123456789:web:abcdef",
  measurementId: "G-ABCDEF"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Initialize Firebase Messaging
const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Received background message:', payload);

  const { notification, data } = payload;
  
  // Customize notification
  const notificationTitle = notification?.title || 'BEC Compare';
  const notificationOptions = {
    body: notification?.body || 'You have a new notification',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/icon-72x72.png',
    image: notification?.image,
    data: {
      ...data,
      timestamp: Date.now(),
      url: data?.actionUrl || '/'
    },
    actions: [
      {
        action: 'view',
        title: 'View Details',
        icon: '/icons/icon-72x72.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
        icon: '/icons/icon-72x72.png'
      }
    ],
    requireInteraction: data?.requireInteraction === 'true',
    silent: data?.silent === 'true',
    tag: data?.tag || 'bec-notification',
    renotify: data?.renotify === 'true',
    vibrate: data?.vibrate ? JSON.parse(data.vibrate) : [200, 100, 200],
    timestamp: Date.now()
  };

  // Show notification
  return self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification click events
self.addEventListener('notificationclick', (event) => {
  console.log('[firebase-messaging-sw.js] Notification click received:', event);

  const { notification, action } = event;
  const data = notification.data || {};

  // Close the notification
  notification.close();

  if (action === 'dismiss') {
    // Just close the notification
    return;
  }

  // Handle view action or notification click
  const urlToOpen = data.url || '/';

  event.waitUntil(
    clients.matchAll({
      type: 'window',
      includeUncontrolled: true
    }).then((clientList) => {
      // Check if there's already a window/tab open with the target URL
      for (const client of clientList) {
        if (client.url === urlToOpen && 'focus' in client) {
          return client.focus();
        }
      }

      // If no existing window/tab, open a new one
      if (clients.openWindow) {
        return clients.openWindow(urlToOpen);
      }
    })
  );

  // Track notification interaction
  trackNotificationInteraction(action || 'click', data);
});

// Handle notification close events
self.addEventListener('notificationclose', (event) => {
  console.log('[firebase-messaging-sw.js] Notification closed:', event);
  
  const data = event.notification.data || {};
  trackNotificationInteraction('close', data);
});

// Track notification interactions
function trackNotificationInteraction(action, data) {
  try {
    // Send tracking data to analytics
    const trackingData = {
      action,
      notificationData: data,
      timestamp: Date.now(),
      userAgent: navigator.userAgent
    };

    // Store in IndexedDB for later sync
    storeAnalyticsData('notification_interaction', trackingData);
  } catch (error) {
    console.error('[firebase-messaging-sw.js] Error tracking interaction:', error);
  }
}

// Store analytics data in IndexedDB
function storeAnalyticsData(eventType, data) {
  const request = indexedDB.open('BECAnalytics', 1);
  
  request.onupgradeneeded = (event) => {
    const db = event.target.result;
    if (!db.objectStoreNames.contains('events')) {
      db.createObjectStore('events', { keyPath: 'id', autoIncrement: true });
    }
  };
  
  request.onsuccess = (event) => {
    const db = event.target.result;
    const transaction = db.transaction(['events'], 'readwrite');
    const store = transaction.objectStore('events');
    
    store.add({
      eventType,
      data,
      timestamp: Date.now(),
      synced: false
    });
  };
  
  request.onerror = (error) => {
    console.error('[firebase-messaging-sw.js] IndexedDB error:', error);
  };
}

// Handle push subscription changes
self.addEventListener('pushsubscriptionchange', (event) => {
  console.log('[firebase-messaging-sw.js] Push subscription changed:', event);
  
  event.waitUntil(
    // Re-subscribe to push notifications
    self.registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: urlBase64ToUint8Array(
        process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY || 'demo-vapid-key'
      )
    }).then((subscription) => {
      console.log('[firebase-messaging-sw.js] Re-subscribed to push notifications');
      
      // Send new subscription to server
      return fetch('/api/notifications/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          subscription: subscription.toJSON(),
          type: 'subscription_change'
        })
      });
    }).catch((error) => {
      console.error('[firebase-messaging-sw.js] Re-subscription failed:', error);
    })
  );
});

// Utility function to convert VAPID key
function urlBase64ToUint8Array(base64String) {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

// Sync analytics data when online
self.addEventListener('sync', (event) => {
  if (event.tag === 'analytics-sync') {
    event.waitUntil(syncAnalyticsData());
  }
});

// Sync stored analytics data
async function syncAnalyticsData() {
  try {
    const request = indexedDB.open('BECAnalytics', 1);
    
    request.onsuccess = async (event) => {
      const db = event.target.result;
      const transaction = db.transaction(['events'], 'readwrite');
      const store = transaction.objectStore('events');
      
      const getAllRequest = store.getAll();
      
      getAllRequest.onsuccess = async () => {
        const events = getAllRequest.result.filter(event => !event.synced);
        
        if (events.length > 0) {
          try {
            const response = await fetch('/api/analytics/batch', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({ events })
            });
            
            if (response.ok) {
              // Mark events as synced
              const updateTransaction = db.transaction(['events'], 'readwrite');
              const updateStore = updateTransaction.objectStore('events');
              
              events.forEach(event => {
                event.synced = true;
                updateStore.put(event);
              });
              
              console.log('[firebase-messaging-sw.js] Analytics data synced successfully');
            }
          } catch (error) {
            console.error('[firebase-messaging-sw.js] Failed to sync analytics data:', error);
          }
        }
      };
    };
  } catch (error) {
    console.error('[firebase-messaging-sw.js] Error syncing analytics data:', error);
  }
}
