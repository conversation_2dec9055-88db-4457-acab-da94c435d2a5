/**
 * Mock Firebase Implementation for Demo Mode
 * Provides local storage-based implementations of Firebase services
 */

// Mock Firebase Auth
export const mockAuth = {
  currentUser: null,
  onAuthStateChanged: (callback) => {
    // Simulate user state from localStorage
    const user = localStorage.getItem('demo-user');
    if (user) {
      mockAuth.currentUser = JSON.parse(user);
      callback(mockAuth.currentUser);
    } else {
      callback(null);
    }
    
    // Return unsubscribe function
    return () => {};
  },
  
  signInWithEmailAndPassword: async (email, password) => {
    // Mock sign in
    const user = {
      uid: 'demo-user-' + Date.now(),
      email,
      displayName: email.split('@')[0],
      emailVerified: true
    };
    
    localStorage.setItem('demo-user', JSON.stringify(user));
    mockAuth.currentUser = user;
    return { user };
  },
  
  createUserWithEmailAndPassword: async (email, password) => {
    return mockAuth.signInWithEmailAndPassword(email, password);
  },
  
  signOut: async () => {
    localStorage.removeItem('demo-user');
    mockAuth.currentUser = null;
  }
};

// Mock Firestore
export const mockFirestore = {
  collection: (path) => ({
    doc: (id) => ({
      set: async (data) => {
        const key = `firestore-${path}-${id}`;
        localStorage.setItem(key, JSON.stringify({
          ...data,
          id,
          createdAt: new Date(),
          updatedAt: new Date()
        }));
        return Promise.resolve();
      },
      
      get: async () => {
        const key = `firestore-${path}-${id}`;
        const data = localStorage.getItem(key);
        return {
          exists: () => !!data,
          data: () => data ? JSON.parse(data) : null,
          id
        };
      },
      
      update: async (updates) => {
        const key = `firestore-${path}-${id}`;
        const existing = localStorage.getItem(key);
        if (existing) {
          const data = JSON.parse(existing);
          const updated = {
            ...data,
            ...updates,
            updatedAt: new Date()
          };
          localStorage.setItem(key, JSON.stringify(updated));
        }
        return Promise.resolve();
      },
      
      delete: async () => {
        const key = `firestore-${path}-${id}`;
        localStorage.removeItem(key);
        return Promise.resolve();
      }
    }),
    
    add: async (data) => {
      const id = 'doc-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
      const key = `firestore-${path}-${id}`;
      localStorage.setItem(key, JSON.stringify({
        ...data,
        id,
        createdAt: new Date(),
        updatedAt: new Date()
      }));
      return { id };
    },
    
    where: (field, operator, value) => ({
      get: async () => {
        // Simple mock query - get all docs and filter
        const docs = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && key.startsWith(`firestore-${path}-`)) {
            const data = JSON.parse(localStorage.getItem(key));
            
            // Simple filtering logic
            let matches = false;
            switch (operator) {
              case '==':
                matches = data[field] === value;
                break;
              case '!=':
                matches = data[field] !== value;
                break;
              case '>':
                matches = data[field] > value;
                break;
              case '<':
                matches = data[field] < value;
                break;
              case '>=':
                matches = data[field] >= value;
                break;
              case '<=':
                matches = data[field] <= value;
                break;
              case 'array-contains':
                matches = Array.isArray(data[field]) && data[field].includes(value);
                break;
            }
            
            if (matches) {
              docs.push({
                id: data.id,
                data: () => data,
                exists: () => true
              });
            }
          }
        }
        
        return { docs };
      }
    }),
    
    get: async () => {
      // Get all documents in collection
      const docs = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(`firestore-${path}-`)) {
          const data = JSON.parse(localStorage.getItem(key));
          docs.push({
            id: data.id,
            data: () => data,
            exists: () => true
          });
        }
      }
      return { docs };
    }
  })
};

// Mock Storage
export const mockStorage = {
  ref: (path) => ({
    put: async (file) => {
      // Convert file to base64 and store in localStorage
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onload = () => {
          const key = `storage-${path}`;
          localStorage.setItem(key, reader.result);
          resolve({
            ref: {
              getDownloadURL: async () => reader.result
            }
          });
        };
        reader.readAsDataURL(file);
      });
    },
    
    getDownloadURL: async () => {
      const key = `storage-${path}`;
      return localStorage.getItem(key) || '';
    },
    
    delete: async () => {
      const key = `storage-${path}`;
      localStorage.removeItem(key);
      return Promise.resolve();
    }
  })
};

// Mock Messaging
export const mockMessaging = {
  getToken: async () => {
    // Return a mock token
    return 'mock-fcm-token-' + Date.now();
  },
  
  onMessage: (callback) => {
    // Store callback for later use
    window.mockFCMCallback = callback;
    return () => {
      delete window.mockFCMCallback;
    };
  },
  
  requestPermission: async () => {
    // Mock permission request
    return 'granted';
  }
};

// Mock Analytics
export const mockAnalytics = {
  logEvent: (eventName, parameters) => {
    console.log('[Mock Analytics]', eventName, parameters);
    
    // Store analytics events in localStorage
    const events = JSON.parse(localStorage.getItem('analytics-events') || '[]');
    events.push({
      eventName,
      parameters,
      timestamp: new Date().toISOString()
    });
    
    // Keep only last 100 events
    if (events.length > 100) {
      events.splice(0, events.length - 100);
    }
    
    localStorage.setItem('analytics-events', JSON.stringify(events));
  }
};

// Utility functions
export const serverTimestamp = () => new Date();

export const arrayUnion = (...elements) => {
  return {
    _type: 'arrayUnion',
    elements
  };
};

// Mock notification sender (for testing)
export const sendMockNotification = (title, body, data = {}) => {
  if (window.mockFCMCallback) {
    window.mockFCMCallback({
      notification: { title, body },
      data
    });
  } else if ('Notification' in window && Notification.permission === 'granted') {
    new Notification(title, {
      body,
      icon: '/icons/icon-192x192.png',
      badge: '/icons/icon-72x72.png',
      data
    });
  }
};

// Demo data generator
export const generateDemoData = () => {
  // Generate some demo user documents
  const demoDocuments = [
    {
      id: 'demo-doc-1',
      userId: 'demo-user',
      name: 'Engineering Transcript',
      type: 'transcript',
      extractedText: 'TRANSCRIPT\nStudent Name: John Doe\nCGPA: 8.5/10\nDegree: Bachelor of Engineering\nUniversity: Demo University',
      structuredData: {
        cgpa: 8.5,
        degree: 'Bachelor of Engineering',
        institution: 'Demo University'
      },
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
      size: 1024000,
      mimeType: 'image/jpeg'
    },
    {
      id: 'demo-doc-2',
      userId: 'demo-user',
      name: '12th Grade Marksheet',
      type: 'marksheet',
      extractedText: 'MARKSHEET\nClass XII\nPercentage: 92%\nTotal Marks: 500\nObtained Marks: 460',
      structuredData: {
        percentage: 92,
        totalMarks: 500,
        obtainedMarks: 460
      },
      createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      size: 856000,
      mimeType: 'image/jpeg'
    }
  ];

  // Store demo documents
  demoDocuments.forEach(doc => {
    const key = `firestore-userDocuments-${doc.id}`;
    localStorage.setItem(key, JSON.stringify(doc));
  });

  // Generate demo user preferences
  const demoUser = {
    uid: 'demo-user',
    email: '<EMAIL>',
    displayName: 'Demo User',
    notificationsEnabled: true,
    notificationPreferences: {
      application_deadline: { enabled: true, frequency: 'daily' },
      status_update: { enabled: true, frequency: 'immediate' },
      personalized_reminder: { enabled: true, frequency: 'weekly' },
      college_event: { enabled: false, frequency: 'daily' },
      placement_update: { enabled: true, frequency: 'weekly' },
      admission_alert: { enabled: true, frequency: 'immediate' }
    },
    favorites: ['rvce', 'msrit', 'pes'],
    searchHistory: [
      {
        query: 'computer science',
        filters: { location: 'bangalore' },
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
      }
    ]
  };

  const userKey = 'firestore-users-demo-user';
  localStorage.setItem(userKey, JSON.stringify(demoUser));

  console.log('[Mock Firebase] Demo data generated');
};

// Initialize demo mode if enabled
if (typeof window !== 'undefined' && process.env.NEXT_PUBLIC_DEMO_MODE === 'true') {
  // Generate demo data on first load
  if (!localStorage.getItem('demo-data-initialized')) {
    generateDemoData();
    localStorage.setItem('demo-data-initialized', 'true');
  }
}
