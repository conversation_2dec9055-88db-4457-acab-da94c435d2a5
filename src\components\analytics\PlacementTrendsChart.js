'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Bar<PERSON>hart, Bar } from 'recharts';
import { TrendingUp, TrendingDown, Filter, BarChart3, LineChart as LineChartIcon } from 'lucide-react';
import AnalyticsCard from './AnalyticsCard';

const PlacementTrendsChart = ({ 
  colleges = [], 
  selectedColleges = [], 
  onCollegeSelect = () => {},
  showComparison = true 
}) => {
  const [chartType, setChartType] = useState('line');
  const [selectedBranch, setSelectedBranch] = useState('all');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Process data for visualization
  const processChartData = () => {
    if (!colleges.length) return [];

    const years = ['2019', '2020', '2021', '2022', '2023'];
    const collegesData = showComparison ? 
      colleges.filter(college => selectedColleges.includes(college.id)) : 
      colleges.slice(0, 5); // Show top 5 if not in comparison mode

    return years.map(year => {
      const yearData = { year };
      
      collegesData.forEach(college => {
        const analytics = college.analytics || college;
        const yearTrend = analytics.placementTrends?.find(trend => trend.year === year);
        if (yearTrend) {
          yearData[college.acronym] = yearTrend.placementRate;
        }
      });
      
      return yearData;
    });
  };

  // Calculate trend statistics
  const calculateTrendStats = () => {
    const collegesData = showComparison ? 
      colleges.filter(college => selectedColleges.includes(college.id)) : 
      colleges.slice(0, 5);

    return collegesData.map(college => {
      const analytics = college.analytics || college;
      const trends = analytics.placementTrends || [];
      
      if (trends.length < 2) return null;
      
      const firstYear = trends[0];
      const lastYear = trends[trends.length - 1];
      const growth = ((lastYear.placementRate - firstYear.placementRate) / firstYear.placementRate) * 100;
      
      return {
        name: college.acronym,
        fullName: college.name,
        currentRate: lastYear.placementRate,
        growth: Math.round(growth * 10) / 10,
        isPositive: growth >= 0,
        totalOffers: lastYear.totalOffers,
        averagePackage: lastYear.averagePackage
      };
    }).filter(Boolean);
  };

  const chartData = processChartData();
  const trendStats = calculateTrendStats();
  const colors = ['#2563eb', '#dc2626', '#059669', '#d97706', '#7c3aed', '#db2777'];

  // Custom tooltip for the chart
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-900 mb-2">{`Year: ${label}`}</p>
          {payload.map((entry, index) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {`${entry.dataKey}: ${entry.value}%`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <AnalyticsCard
      title="Placement Trends Analysis"
      subtitle="5-year placement rate trends and growth patterns"
      info="Track placement performance over time and identify growth trends"
      loading={loading}
      error={error}
      className="col-span-full"
    >
      <div className="space-y-6">
        {/* Controls */}
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <select
                value={selectedBranch}
                onChange={(e) => setSelectedBranch(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="all">All Branches</option>
                <option value="cse">Computer Science</option>
                <option value="ece">Electronics & Communication</option>
                <option value="mechanical">Mechanical</option>
                <option value="civil">Civil</option>
              </select>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setChartType('line')}
              className={`p-2 rounded-lg transition-colors ${
                chartType === 'line' 
                  ? 'bg-primary-100 text-primary-600' 
                  : 'text-gray-500 hover:bg-gray-100'
              }`}
              title="Line Chart"
            >
              <LineChartIcon className="h-4 w-4" />
            </button>
            <button
              onClick={() => setChartType('bar')}
              className={`p-2 rounded-lg transition-colors ${
                chartType === 'bar' 
                  ? 'bg-primary-100 text-primary-600' 
                  : 'text-gray-500 hover:bg-gray-100'
              }`}
              title="Bar Chart"
            >
              <BarChart3 className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Chart */}
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            {chartType === 'line' ? (
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis 
                  dataKey="year" 
                  stroke="#6b7280"
                  fontSize={12}
                />
                <YAxis 
                  stroke="#6b7280"
                  fontSize={12}
                  domain={['dataMin - 5', 'dataMax + 5']}
                  label={{ value: 'Placement Rate (%)', angle: -90, position: 'insideLeft' }}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                {Object.keys(chartData[0] || {}).filter(key => key !== 'year').map((college, index) => (
                  <Line
                    key={college}
                    type="monotone"
                    dataKey={college}
                    stroke={colors[index % colors.length]}
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                ))}
              </LineChart>
            ) : (
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis 
                  dataKey="year" 
                  stroke="#6b7280"
                  fontSize={12}
                />
                <YAxis 
                  stroke="#6b7280"
                  fontSize={12}
                  label={{ value: 'Placement Rate (%)', angle: -90, position: 'insideLeft' }}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                {Object.keys(chartData[0] || {}).filter(key => key !== 'year').map((college, index) => (
                  <Bar
                    key={college}
                    dataKey={college}
                    fill={colors[index % colors.length]}
                    radius={[2, 2, 0, 0]}
                  />
                ))}
              </BarChart>
            )}
          </ResponsiveContainer>
        </div>

        {/* Trend Statistics */}
        {trendStats.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {trendStats.map((stat, index) => (
              <div key={stat.name} className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-gray-900">{stat.name}</h4>
                  <div className={`flex items-center space-x-1 ${
                    stat.isPositive ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stat.isPositive ? (
                      <TrendingUp className="h-4 w-4" />
                    ) : (
                      <TrendingDown className="h-4 w-4" />
                    )}
                    <span className="text-sm font-medium">{stat.growth}%</span>
                  </div>
                </div>
                <div className="space-y-1 text-sm text-gray-600">
                  <p>Current Rate: <span className="font-medium">{stat.currentRate}%</span></p>
                  <p>Total Offers: <span className="font-medium">{stat.totalOffers}</span></p>
                  <p>Avg Package: <span className="font-medium">₹{stat.averagePackage} LPA</span></p>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* College Selection for Comparison */}
        {showComparison && colleges.length > 0 && (
          <div className="border-t pt-4">
            <h4 className="font-medium text-gray-900 mb-3">Select Colleges to Compare:</h4>
            <div className="flex flex-wrap gap-2">
              {colleges.slice(0, 10).map(college => (
                <button
                  key={college.id}
                  onClick={() => onCollegeSelect(college.id)}
                  className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    selectedColleges.includes(college.id)
                      ? 'bg-primary-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {college.acronym}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </AnalyticsCard>
  );
};

export default PlacementTrendsChart;
