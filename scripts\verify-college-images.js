const fs = require('fs');
const path = require('path');

// Load college data
const collegeData = JSON.parse(fs.readFileSync('college.json', 'utf8'));
const publicCollegeData = JSON.parse(fs.readFileSync('public/colleges.json', 'utf8'));

// Get list of image files
const imagesDir = 'public/images/colleges';
const imageFiles = fs.readdirSync(imagesDir).filter(file => file.endsWith('.jpg'));

console.log('🔍 Verifying College Images...\n');

// Check if all colleges have images
let missingImages = [];
let foundImages = [];
let duplicateImages = [];

// Track image usage
const imageUsage = {};

collegeData.forEach((college, index) => {
  const imagePath = college.image;
  const imageName = path.basename(imagePath);
  
  // Check if image file exists
  const imageExists = imageFiles.includes(imageName);
  
  if (imageExists) {
    foundImages.push({
      id: college.id,
      name: college.name,
      acronym: college.acronym,
      image: imageName
    });
    
    // Track usage for duplicate detection
    if (imageUsage[imageName]) {
      imageUsage[imageName].push(college.id);
    } else {
      imageUsage[imageName] = [college.id];
    }
  } else {
    missingImages.push({
      id: college.id,
      name: college.name,
      acronym: college.acronym,
      expectedImage: imageName
    });
  }
});

// Find duplicates
Object.entries(imageUsage).forEach(([imageName, collegeIds]) => {
  if (collegeIds.length > 1) {
    duplicateImages.push({
      image: imageName,
      usedBy: collegeIds
    });
  }
});

// Report results
console.log(`📊 VERIFICATION RESULTS:`);
console.log(`Total Colleges: ${collegeData.length}`);
console.log(`Total Image Files: ${imageFiles.length}`);
console.log(`Images Found: ${foundImages.length}`);
console.log(`Images Missing: ${missingImages.length}`);
console.log(`Duplicate Images: ${duplicateImages.length}\n`);

// Show missing images
if (missingImages.length > 0) {
  console.log('❌ MISSING IMAGES:');
  missingImages.forEach(college => {
    console.log(`  ID ${college.id}: ${college.name} (${college.acronym}) - Expected: ${college.expectedImage}`);
  });
  console.log('');
}

// Show duplicate images
if (duplicateImages.length > 0) {
  console.log('⚠️  DUPLICATE IMAGES:');
  duplicateImages.forEach(duplicate => {
    console.log(`  ${duplicate.image} used by colleges: ${duplicate.usedBy.join(', ')}`);
  });
  console.log('');
}

// Show colleges 42-50 specifically
console.log('🎯 COLLEGES 42-50 STATUS:');
const colleges42to50 = collegeData.filter(college => college.id >= 42 && college.id <= 50);
colleges42to50.forEach(college => {
  const imageName = path.basename(college.image);
  const imageExists = imageFiles.includes(imageName);
  const status = imageExists ? '✅' : '❌';
  console.log(`  ${status} ID ${college.id}: ${college.name} (${college.acronym}) - ${imageName}`);
});

// Check consistency between college.json and public/colleges.json
console.log('\n🔄 CHECKING DATA CONSISTENCY:');
let inconsistencies = [];

collegeData.forEach(college => {
  const publicCollege = publicCollegeData.find(pc => pc.id === college.id);
  if (publicCollege && publicCollege.image !== college.image) {
    inconsistencies.push({
      id: college.id,
      name: college.name,
      collegeJson: college.image,
      publicJson: publicCollege.image
    });
  }
});

if (inconsistencies.length === 0) {
  console.log('✅ college.json and public/colleges.json are consistent');
} else {
  console.log('❌ INCONSISTENCIES FOUND:');
  inconsistencies.forEach(inc => {
    console.log(`  ID ${inc.id}: ${inc.name}`);
    console.log(`    college.json: ${inc.collegeJson}`);
    console.log(`    public/colleges.json: ${inc.publicJson}`);
  });
}

// Final summary
console.log('\n📋 SUMMARY:');
if (missingImages.length === 0 && duplicateImages.length === 0 && inconsistencies.length === 0) {
  console.log('🎉 ALL CHECKS PASSED! All college images are properly configured.');
} else {
  console.log('⚠️  Issues found that need attention:');
  if (missingImages.length > 0) console.log(`   - ${missingImages.length} missing images`);
  if (duplicateImages.length > 0) console.log(`   - ${duplicateImages.length} duplicate images`);
  if (inconsistencies.length > 0) console.log(`   - ${inconsistencies.length} data inconsistencies`);
}
