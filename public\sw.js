// Enhanced Service Worker for PWA functionality and comprehensive offline caching

// Cache version management - increment when updating cache strategy
const CACHE_VERSION = 'v2.0.0';
const STATIC_CACHE_NAME = `bec-static-${CACHE_VERSION}`;
const DYNAMIC_CACHE_NAME = `bec-dynamic-${CACHE_VERSION}`;
const IMAGE_CACHE_NAME = `bec-images-${CACHE_VERSION}`;
const API_CACHE_NAME = `bec-api-${CACHE_VERSION}`;

// Static assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/colleges',
  '/compare',
  '/analytics',
  '/offline',
  '/colleges.json',
  '/manifest.json',
  '/_next/static/css/',
  '/_next/static/js/',
];

// API endpoints to cache
const API_ENDPOINTS = [
  '/api/colleges',
  '/api/search',
  '/api/analytics'
];

// Maximum cache sizes to prevent storage overflow
const MAX_CACHE_SIZE = {
  images: 50,
  dynamic: 100,
  api: 25
};

// Utility functions for cache management
const limitCacheSize = async (cacheName, maxSize) => {
  const cache = await caches.open(cacheName);
  const keys = await cache.keys();
  if (keys.length > maxSize) {
    const keysToDelete = keys.slice(0, keys.length - maxSize);
    await Promise.all(keysToDelete.map(key => cache.delete(key)));
  }
};

const isOnline = () => {
  return navigator.onLine;
};

// Install event - cache static resources and set up PWA
self.addEventListener('install', (event) => {
  console.log('[SW] Installing service worker...');

  event.waitUntil(
    Promise.all([
      // Cache static assets
      caches.open(STATIC_CACHE_NAME).then((cache) => {
        console.log('[SW] Caching static assets');
        return cache.addAll(STATIC_ASSETS.filter(url => url !== '/_next/static/css/' && url !== '/_next/static/js/'));
      }),

      // Pre-cache critical college data
      caches.open(API_CACHE_NAME).then(async (cache) => {
        console.log('[SW] Pre-caching college data');
        try {
          const collegeResponse = await fetch('/colleges.json');
          if (collegeResponse.ok) {
            await cache.put('/colleges.json', collegeResponse);
          }
        } catch (error) {
          console.warn('[SW] Failed to pre-cache college data:', error);
        }
      })
    ])
  );

  // Force activation of new service worker
  self.skipWaiting();
});

// Activate event - clean up old caches and claim clients
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating service worker...');

  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        const validCacheNames = [STATIC_CACHE_NAME, DYNAMIC_CACHE_NAME, IMAGE_CACHE_NAME, API_CACHE_NAME];
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (!validCacheNames.includes(cacheName)) {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),

      // Limit cache sizes
      limitCacheSize(IMAGE_CACHE_NAME, MAX_CACHE_SIZE.images),
      limitCacheSize(DYNAMIC_CACHE_NAME, MAX_CACHE_SIZE.dynamic),
      limitCacheSize(API_CACHE_NAME, MAX_CACHE_SIZE.api)
    ])
  );

  // Take control of all clients immediately
  self.clients.claim();
});

// Enhanced fetch event with comprehensive caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests and chrome-extension requests
  if (request.method !== 'GET' || url.protocol === 'chrome-extension:') {
    return;
  }

  // Handle different types of requests with appropriate caching strategies

  // 1. College images - Cache-first strategy with fallback
  if (url.pathname.startsWith('/images/colleges/')) {
    event.respondWith(handleImageRequest(request));
    return;
  }

  // 2. Static assets - Cache-first strategy
  if (url.pathname.startsWith('/_next/static/') ||
      url.pathname.startsWith('/icons/') ||
      url.pathname.endsWith('.css') ||
      url.pathname.endsWith('.js')) {
    event.respondWith(handleStaticAssetRequest(request));
    return;
  }

  // 3. API endpoints and college data - Network-first with cache fallback
  if (url.pathname === '/colleges.json' ||
      API_ENDPOINTS.some(endpoint => url.pathname.startsWith(endpoint))) {
    event.respondWith(handleAPIRequest(request));
    return;
  }

  // 4. Navigation requests - Network-first with offline fallback
  if (request.mode === 'navigate') {
    event.respondWith(handleNavigationRequest(request));
    return;
  }

  // 5. Other requests - Network-first strategy
  event.respondWith(handleOtherRequests(request));
});

// Handler functions for different request types

// Handle image requests with cache-first strategy
async function handleImageRequest(request) {
  try {
    const cache = await caches.open(IMAGE_CACHE_NAME);
    const cachedResponse = await cache.match(request);

    if (cachedResponse) {
      return cachedResponse;
    }

    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      await cache.put(request, networkResponse.clone());
      await limitCacheSize(IMAGE_CACHE_NAME, MAX_CACHE_SIZE.images);
    }
    return networkResponse;
  } catch (error) {
    console.warn('[SW] Image request failed:', error);
    return createFallbackImageResponse();
  }
}

// Handle static asset requests with cache-first strategy
async function handleStaticAssetRequest(request) {
  try {
    const cache = await caches.open(STATIC_CACHE_NAME);
    const cachedResponse = await cache.match(request);

    if (cachedResponse) {
      return cachedResponse;
    }

    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      await cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.warn('[SW] Static asset request failed:', error);
    throw error;
  }
}

// Handle API requests with network-first strategy
async function handleAPIRequest(request) {
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(API_CACHE_NAME);
      await cache.put(request, networkResponse.clone());
      await limitCacheSize(API_CACHE_NAME, MAX_CACHE_SIZE.api);
    }
    return networkResponse;
  } catch (error) {
    console.warn('[SW] Network request failed, trying cache:', error);
    const cache = await caches.open(API_CACHE_NAME);
    const cachedResponse = await cache.match(request);

    if (cachedResponse) {
      return cachedResponse;
    }

    throw error;
  }
}

// Handle navigation requests with offline fallback
async function handleNavigationRequest(request) {
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      await cache.put(request, networkResponse.clone());
      await limitCacheSize(DYNAMIC_CACHE_NAME, MAX_CACHE_SIZE.dynamic);
    }
    return networkResponse;
  } catch (error) {
    console.warn('[SW] Navigation request failed, serving offline page:', error);

    // Try to serve cached version first
    const cache = await caches.open(DYNAMIC_CACHE_NAME);
    const cachedResponse = await cache.match(request);

    if (cachedResponse) {
      return cachedResponse;
    }

    // Serve offline fallback page
    const offlineResponse = await cache.match('/offline');
    if (offlineResponse) {
      return offlineResponse;
    }

    // Last resort: create a basic offline response
    return createOfflineResponse();
  }
}

// Handle other requests with network-first strategy
async function handleOtherRequests(request) {
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      await cache.put(request, networkResponse.clone());
      await limitCacheSize(DYNAMIC_CACHE_NAME, MAX_CACHE_SIZE.dynamic);
    }
    return networkResponse;
  } catch (error) {
    const cache = await caches.open(DYNAMIC_CACHE_NAME);
    const cachedResponse = await cache.match(request);
    return cachedResponse || Response.error();
  }
}

// Utility functions for fallback responses
function createFallbackImageResponse() {
  const svg = `
    <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#f8fafc"/>
      <rect x="50" y="50" width="300" height="200" fill="#e2e8f0" rx="8"/>
      <circle cx="120" cy="120" r="20" fill="#cbd5e1"/>
      <rect x="160" y="110" width="120" height="8" fill="#cbd5e1" rx="4"/>
      <rect x="160" y="130" width="80" height="8" fill="#e2e8f0" rx="4"/>
      <text x="200" y="220" text-anchor="middle" fill="#64748b" font-family="Arial, sans-serif" font-size="12">
        College Image
      </text>
    </svg>
  `;

  return new Response(svg, {
    headers: {
      'Content-Type': 'image/svg+xml',
      'Cache-Control': 'no-cache'
    }
  });
}

function createOfflineResponse() {
  const html = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Offline - BEC Compare</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          margin: 0; padding: 2rem; background: #f8fafc; color: #334155;
          display: flex; flex-direction: column; align-items: center; justify-content: center;
          min-height: 100vh; text-align: center;
        }
        .offline-icon { font-size: 4rem; margin-bottom: 1rem; }
        h1 { color: #2563eb; margin-bottom: 0.5rem; }
        p { margin-bottom: 1.5rem; max-width: 400px; line-height: 1.6; }
        .retry-btn {
          background: #2563eb; color: white; border: none; padding: 0.75rem 1.5rem;
          border-radius: 0.5rem; cursor: pointer; font-size: 1rem;
        }
        .retry-btn:hover { background: #1d4ed8; }
      </style>
    </head>
    <body>
      <div class="offline-icon">📱</div>
      <h1>You're Offline</h1>
      <p>It looks like you're not connected to the internet. Some features may not be available, but you can still browse cached college data.</p>
      <button class="retry-btn" onclick="window.location.reload()">Try Again</button>
    </body>
    </html>
  `;

  return new Response(html, {
    headers: {
      'Content-Type': 'text/html',
      'Cache-Control': 'no-cache'
    }
  });
}

// Background sync for when connection is restored
self.addEventListener('sync', (event) => {
  console.log('[SW] Background sync triggered:', event.tag);

  if (event.tag === 'background-sync') {
    event.waitUntil(handleBackgroundSync());
  }
});

async function handleBackgroundSync() {
  try {
    // Sync user preferences and search history when online
    console.log('[SW] Performing background sync...');

    // This will be implemented with the offline sync utility
    const syncEvent = new CustomEvent('sw-background-sync', {
      detail: { timestamp: Date.now() }
    });

    // Notify all clients about the sync
    const clients = await self.clients.matchAll();
    clients.forEach(client => {
      client.postMessage({
        type: 'BACKGROUND_SYNC',
        payload: { success: true, timestamp: Date.now() }
      });
    });

  } catch (error) {
    console.error('[SW] Background sync failed:', error);
  }
}
