'use client';

import { useEffect } from 'react';

const ServiceWorkerRegistration = () => {
  useEffect(() => {
    if ('serviceWorker' in navigator) {
      // Register main service worker (in development and production)
      navigator.serviceWorker
        .register('/sw.js')
        .then((registration) => {
          console.log('Service Worker registered successfully:', registration);
        })
        .catch((error) => {
          console.log('Service Worker registration failed:', error);
        });

      // Register Firebase messaging service worker if not in demo mode
      if (process.env.NEXT_PUBLIC_DEMO_MODE !== 'true') {
        navigator.serviceWorker.register('/firebase-messaging-sw.js')
          .then((registration) => {
            console.log('Firebase Messaging SW registered successfully:', registration);
          })
          .catch((error) => {
            console.error('Firebase Messaging SW registration failed:', error);
          });
      }
    }
  }, []);

  return null; // This component doesn't render anything
};

export default ServiceWorkerRegistration;
