// Advanced Analytics Data and Utilities for College Comparison Platform

// Generate realistic mock data for analytics features
const generatePlacementTrends = (collegeId, basePlacementRate) => {
  const years = ['2019', '2020', '2021', '2022', '2023'];
  const trends = [];
  
  years.forEach((year, index) => {
    // Add realistic variation based on COVID impact and recovery
    let variation = 0;
    if (year === '2020') variation = -8; // COVID impact
    else if (year === '2021') variation = -5; // Recovery phase
    else if (year === '2022') variation = 2; // Growth
    else if (year === '2023') variation = 5; // Strong recovery
    
    const rate = Math.max(40, Math.min(98, basePlacementRate + variation + (Math.random() - 0.5) * 6));
    
    trends.push({
      year,
      placementRate: Math.round(rate * 10) / 10,
      totalOffers: Math.round((rate / 100) * (800 + Math.random() * 400)),
      averagePackage: Math.round((8 + Math.random() * 12) * 10) / 10
    });
  });
  
  return trends;
};

// Generate branch-wise salary distribution
const generateSalaryDistribution = (collegeRanking) => {
  const branches = [
    'Computer Science',
    'Electronics & Communication',
    'Mechanical',
    'Civil',
    'Electrical',
    'Information Science',
    'Biotechnology',
    'Chemical'
  ];
  
  const baseSalary = Math.max(4, 20 - collegeRanking * 0.3);
  
  return branches.map(branch => {
    let multiplier = 1;
    if (branch === 'Computer Science') multiplier = 1.4;
    else if (branch === 'Information Science') multiplier = 1.3;
    else if (branch === 'Electronics & Communication') multiplier = 1.2;
    else if (branch === 'Electrical') multiplier = 1.1;
    else if (branch === 'Mechanical') multiplier = 0.9;
    else if (branch === 'Civil') multiplier = 0.8;
    else if (branch === 'Biotechnology') multiplier = 0.85;
    else if (branch === 'Chemical') multiplier = 0.95;
    
    const avgSalary = baseSalary * multiplier;
    
    return {
      branch,
      min: Math.round((avgSalary * 0.6) * 10) / 10,
      q1: Math.round((avgSalary * 0.8) * 10) / 10,
      median: Math.round(avgSalary * 10) / 10,
      q3: Math.round((avgSalary * 1.3) * 10) / 10,
      max: Math.round((avgSalary * 2.1) * 10) / 10,
      average: Math.round(avgSalary * 10) / 10,
      count: Math.round(50 + Math.random() * 150)
    };
  });
};

// Generate career path data
const generateCareerPaths = (collegeRanking) => {
  const companies = [
    { name: 'Microsoft', tier: 'Tier1', avgSalary: 45 },
    { name: 'Google', tier: 'Tier1', avgSalary: 50 },
    { name: 'Amazon', tier: 'Tier1', avgSalary: 42 },
    { name: 'Goldman Sachs', tier: 'Tier1', avgSalary: 55 },
    { name: 'Flipkart', tier: 'Tier1', avgSalary: 38 },
    { name: 'Infosys', tier: 'Tier2', avgSalary: 12 },
    { name: 'TCS', tier: 'Tier2', avgSalary: 10 },
    { name: 'Wipro', tier: 'Tier2', avgSalary: 11 },
    { name: 'Accenture', tier: 'Tier2', avgSalary: 15 },
    { name: 'Capgemini', tier: 'Tier2', avgSalary: 13 },
    { name: 'L&T', tier: 'Core', avgSalary: 14 },
    { name: 'Bosch', tier: 'Core', avgSalary: 16 },
    { name: 'Siemens', tier: 'Core', avgSalary: 18 },
    { name: 'Honeywell', tier: 'Core', avgSalary: 17 }
  ];
  
  // Better colleges get more tier1 placements
  const tier1Probability = Math.max(0.1, 0.8 - (collegeRanking - 1) * 0.05);
  
  return companies.map(company => {
    let placementCount = 0;
    if (company.tier === 'Tier1') {
      placementCount = Math.round(tier1Probability * (20 + Math.random() * 30));
    } else if (company.tier === 'Tier2') {
      placementCount = Math.round((1 - tier1Probability * 0.5) * (40 + Math.random() * 60));
    } else {
      placementCount = Math.round(0.3 * (15 + Math.random() * 25));
    }
    
    return {
      ...company,
      placementCount,
      growthRate: Math.round((5 + Math.random() * 15) * 10) / 10
    };
  }).filter(company => company.placementCount > 0);
};

// ROI calculation parameters
const roiParameters = {
  averageCareerGrowth: 0.12, // 12% annual salary growth
  inflationRate: 0.06, // 6% annual inflation
  taxRate: 0.3, // 30% effective tax rate
  livingExpenses: {
    bangalore: 180000, // Annual living expenses in Bangalore
    other: 120000 // Annual living expenses in other cities
  }
};

// Admission difficulty scoring
const calculateAdmissionDifficulty = (college) => {
  let score = 0;
  
  // Ranking factor (40% weightage)
  score += (50 - college.ranking) * 0.8;
  
  // NIRF ranking factor (30% weightage)
  if (college.nirf && college.nirf !== 'N/A') {
    const nirfRank = parseInt(college.nirf.split('-')[0]) || parseInt(college.nirf);
    score += (200 - nirfRank) * 0.15;
  }
  
  // Placement rate factor (20% weightage)
  score += college.placementRate * 0.2;
  
  // Package factor (10% weightage)
  score += college.highestPackage * 0.1;
  
  // Normalize to 0-100 scale
  score = Math.max(0, Math.min(100, score));
  
  let difficulty = 'Easy';
  let probability = 85;
  
  if (score >= 80) {
    difficulty = 'Very Hard';
    probability = 15;
  } else if (score >= 65) {
    difficulty = 'Hard';
    probability = 35;
  } else if (score >= 45) {
    difficulty = 'Medium';
    probability = 60;
  } else if (score >= 25) {
    difficulty = 'Easy';
    probability = 80;
  } else {
    difficulty = 'Very Easy';
    probability = 95;
  }
  
  return {
    score: Math.round(score),
    difficulty,
    probability,
    cutoffTrend: generateCutoffTrends(score)
  };
};

// Generate cutoff trends for admission difficulty
const generateCutoffTrends = (difficultyScore) => {
  const years = ['2019', '2020', '2021', '2022', '2023'];
  const baseCutoff = 60 + (difficultyScore * 0.3);
  
  return years.map((year, index) => ({
    year,
    cutoff: Math.round((baseCutoff + (Math.random() - 0.5) * 8) * 10) / 10,
    applicants: Math.round(1000 + difficultyScore * 50 + Math.random() * 500),
    seats: Math.round(60 + Math.random() * 120)
  }));
};

// Main analytics data generator
export const generateCollegeAnalytics = (college) => {
  return {
    id: college.id,
    name: college.name,
    acronym: college.acronym,
    placementTrends: generatePlacementTrends(college.id, college.placementRate),
    salaryDistribution: generateSalaryDistribution(college.ranking),
    careerPaths: generateCareerPaths(college.ranking),
    admissionDifficulty: calculateAdmissionDifficulty(college),
    roiData: {
      totalFees: college.fees || (200000 + college.ranking * 10000),
      averageStartingSalary: college.averagePackage || (college.highestPackage * 0.6),
      expectedGrowth: roiParameters.averageCareerGrowth
    }
  };
};

// ROI Calculator utility functions
export const calculateROI = (fees, startingSalary, years = 5) => {
  const { averageCareerGrowth, inflationRate, taxRate, livingExpenses } = roiParameters;
  
  let totalEarnings = 0;
  let currentSalary = startingSalary;
  
  for (let year = 1; year <= years; year++) {
    const netSalary = currentSalary * (1 - taxRate);
    const disposableIncome = netSalary - livingExpenses.bangalore;
    totalEarnings += Math.max(0, disposableIncome);
    currentSalary *= (1 + averageCareerGrowth);
  }
  
  const netROI = totalEarnings - fees;
  const roiPercentage = (netROI / fees) * 100;
  const breakEvenMonths = fees / ((startingSalary * (1 - taxRate) - livingExpenses.bangalore) / 12);
  
  return {
    totalInvestment: fees,
    totalEarnings: Math.round(totalEarnings),
    netROI: Math.round(netROI),
    roiPercentage: Math.round(roiPercentage * 10) / 10,
    breakEvenMonths: Math.round(breakEvenMonths),
    yearlyBreakdown: generateYearlyROIBreakdown(fees, startingSalary, years)
  };
};

// Generate yearly ROI breakdown for visualization
const generateYearlyROIBreakdown = (fees, startingSalary, years) => {
  const { averageCareerGrowth, taxRate, livingExpenses } = roiParameters;
  
  let cumulativeEarnings = -fees; // Start with negative investment
  let currentSalary = startingSalary;
  const breakdown = [];
  
  for (let year = 1; year <= years; year++) {
    const netSalary = currentSalary * (1 - taxRate);
    const disposableIncome = netSalary - livingExpenses.bangalore;
    cumulativeEarnings += Math.max(0, disposableIncome);
    
    breakdown.push({
      year,
      salary: Math.round(currentSalary),
      netSalary: Math.round(netSalary),
      disposableIncome: Math.round(Math.max(0, disposableIncome)),
      cumulativeROI: Math.round(cumulativeEarnings)
    });
    
    currentSalary *= (1 + averageCareerGrowth);
  }
  
  return breakdown;
};

// Analytics aggregation utilities
export const getAnalyticsOverview = async (colleges) => {
  const analytics = colleges.map(generateCollegeAnalytics);

  return {
    totalColleges: colleges.length,
    averagePlacementRate: Math.round(
      analytics.reduce((sum, a) => sum + a.placementTrends[a.placementTrends.length - 1].placementRate, 0) / analytics.length * 10
    ) / 10,
    topPerformers: analytics
      .sort((a, b) => b.placementTrends[b.placementTrends.length - 1].placementRate - a.placementTrends[a.placementTrends.length - 1].placementRate)
      .slice(0, 5),
    industryDistribution: calculateIndustryDistribution(analytics),
    salaryTrends: calculateOverallSalaryTrends(analytics)
  };
};

// Calculate industry distribution across all colleges
const calculateIndustryDistribution = (analytics) => {
  const industries = {
    'IT Services': 0,
    'Product Companies': 0,
    'Core Engineering': 0,
    'Consulting': 0,
    'Finance': 0,
    'Startups': 0
  };

  analytics.forEach(college => {
    college.careerPaths.forEach(company => {
      if (['TCS', 'Infosys', 'Wipro', 'Capgemini', 'Accenture'].includes(company.name)) {
        industries['IT Services'] += company.placementCount;
      } else if (['Microsoft', 'Google', 'Amazon', 'Flipkart'].includes(company.name)) {
        industries['Product Companies'] += company.placementCount;
      } else if (['L&T', 'Bosch', 'Siemens', 'Honeywell'].includes(company.name)) {
        industries['Core Engineering'] += company.placementCount;
      } else if (company.name === 'Goldman Sachs') {
        industries['Finance'] += company.placementCount;
      }
    });
  });

  return Object.entries(industries).map(([industry, count]) => ({
    industry,
    count,
    percentage: Math.round((count / Object.values(industries).reduce((a, b) => a + b, 0)) * 100)
  }));
};

// Calculate overall salary trends
const calculateOverallSalaryTrends = (analytics) => {
  const years = ['2019', '2020', '2021', '2022', '2023'];

  return years.map(year => {
    const yearData = analytics.map(college =>
      college.placementTrends.find(trend => trend.year === year)
    ).filter(Boolean);

    return {
      year,
      averagePackage: Math.round(
        yearData.reduce((sum, data) => sum + data.averagePackage, 0) / yearData.length * 10
      ) / 10,
      totalOffers: yearData.reduce((sum, data) => sum + data.totalOffers, 0)
    };
  });
};

// Comparison utilities for multiple colleges
export const compareCollegeAnalytics = (colleges) => {
  const analytics = colleges.map(generateCollegeAnalytics);

  return {
    placementComparison: analytics.map(college => ({
      name: college.acronym,
      currentRate: college.placementTrends[college.placementTrends.length - 1].placementRate,
      trend: college.placementTrends,
      growth: calculateGrowthRate(college.placementTrends)
    })),
    salaryComparison: analytics.map(college => ({
      name: college.acronym,
      averageSalary: college.salaryDistribution.reduce((sum, branch) => sum + branch.average, 0) / college.salaryDistribution.length,
      topBranchSalary: Math.max(...college.salaryDistribution.map(branch => branch.average))
    })),
    roiComparison: analytics.map(college => {
      const roi = calculateROI(college.roiData.totalFees, college.roiData.averageStartingSalary);
      return {
        name: college.acronym,
        roiPercentage: roi.roiPercentage,
        breakEvenMonths: roi.breakEvenMonths,
        totalFees: college.roiData.totalFees
      };
    })
  };
};

// Calculate growth rate from trends
const calculateGrowthRate = (trends) => {
  if (trends.length < 2) return 0;
  const firstYear = trends[0].placementRate;
  const lastYear = trends[trends.length - 1].placementRate;
  return Math.round(((lastYear - firstYear) / firstYear) * 100 * 10) / 10;
};

// Filter and search utilities for analytics
export const filterAnalyticsByBranch = (analytics, branch) => {
  return analytics.map(college => ({
    ...college,
    salaryDistribution: college.salaryDistribution.filter(dist =>
      dist.branch.toLowerCase().includes(branch.toLowerCase())
    )
  }));
};

export const searchAnalyticsByCompany = (analytics, companyName) => {
  return analytics.map(college => ({
    ...college,
    careerPaths: college.careerPaths.filter(company =>
      company.name.toLowerCase().includes(companyName.toLowerCase())
    )
  })).filter(college => college.careerPaths.length > 0);
};

export { roiParameters };
