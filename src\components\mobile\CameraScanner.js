'use client';

import { useState, useRef, useEffect } from 'react';
import {
  Camera,
  X,
  RotateCcw,
  Zap,
  ZapOff,
  Download,
  FileText,
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import cameraManager, { extractTextFromImage, detectDocumentType, extractDocumentData } from '../../lib/cameraUtils';

const CameraScanner = ({ 
  onCapture, 
  onClose, 
  onTextExtracted,
  autoExtractText = true,
  showPreview = true 
}) => {
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  
  const [isActive, setIsActive] = useState(false);
  const [isCapturing, setIsCapturing] = useState(false);
  const [capturedImage, setCapturedImage] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [extractedText, setExtractedText] = useState('');
  const [documentData, setDocumentData] = useState(null);
  const [error, setError] = useState('');
  const [facingMode, setFacingMode] = useState('environment');
  const [flashEnabled, setFlashEnabled] = useState(false);
  const [step, setStep] = useState('camera'); // camera, preview, processing, result

  useEffect(() => {
    startCamera();
    
    return () => {
      stopCamera();
    };
  }, []);

  // Start camera
  const startCamera = async () => {
    try {
      setError('');
      const stream = await cameraManager.startCamera(videoRef.current, {
        facingMode,
        torch: flashEnabled
      });
      setIsActive(true);
    } catch (error) {
      setError(error.message);
      console.error('Camera start error:', error);
    }
  };

  // Stop camera
  const stopCamera = () => {
    cameraManager.stopCamera();
    setIsActive(false);
  };

  // Capture image
  const handleCapture = async () => {
    if (!isActive || isCapturing) return;
    
    setIsCapturing(true);
    
    try {
      // Add capture animation
      const flash = document.createElement('div');
      flash.className = 'fixed inset-0 bg-white z-50 pointer-events-none';
      flash.style.animation = 'flash 0.3s ease-out';
      document.body.appendChild(flash);
      
      setTimeout(() => flash.remove(), 300);
      
      // Capture image
      const imageBlob = await cameraManager.captureImage(videoRef.current, 0.9);
      const imageDataURL = await cameraManager.captureImageAsDataURL(videoRef.current, 0.9);
      
      setCapturedImage({
        blob: imageBlob,
        dataURL: imageDataURL,
        timestamp: new Date()
      });
      
      setStep('preview');
      
      // Haptic feedback
      if ('vibrate' in navigator) {
        navigator.vibrate(50);
      }
      
      // Auto-extract text if enabled
      if (autoExtractText) {
        await processImage(imageBlob);
      }
      
    } catch (error) {
      setError('Failed to capture image. Please try again.');
      console.error('Capture error:', error);
    } finally {
      setIsCapturing(false);
    }
  };

  // Process captured image for text extraction
  const processImage = async (imageBlob) => {
    setIsProcessing(true);
    setStep('processing');
    
    try {
      // Extract text using OCR
      const ocrResult = await extractTextFromImage(imageBlob);
      
      if (ocrResult.success && ocrResult.text) {
        setExtractedText(ocrResult.text);
        
        // Detect document type
        const docType = detectDocumentType(ocrResult.text);
        
        // Extract structured data
        const structuredData = extractDocumentData(ocrResult.text, docType.type);
        setDocumentData(structuredData);
        
        setStep('result');
        
        // Callback with extracted data
        if (onTextExtracted) {
          onTextExtracted({
            text: ocrResult.text,
            confidence: ocrResult.confidence,
            documentType: docType,
            structuredData,
            image: capturedImage
          });
        }
      } else {
        throw new Error('No text found in the image');
      }
    } catch (error) {
      setError('Failed to extract text from image. Please try again.');
      console.error('OCR error:', error);
      setStep('preview');
    } finally {
      setIsProcessing(false);
    }
  };

  // Switch camera (front/back)
  const switchCamera = async () => {
    const newFacingMode = facingMode === 'environment' ? 'user' : 'environment';
    setFacingMode(newFacingMode);
    
    stopCamera();
    
    try {
      await cameraManager.switchCamera(videoRef.current, newFacingMode);
      setIsActive(true);
    } catch (error) {
      setError('Failed to switch camera');
      console.error('Camera switch error:', error);
    }
  };

  // Toggle flash (if supported)
  const toggleFlash = () => {
    setFlashEnabled(!flashEnabled);
    // Note: Flash control via MediaStream API is limited
    // This would need native app implementation for full flash control
  };

  // Retake photo
  const retakePhoto = () => {
    setCapturedImage(null);
    setExtractedText('');
    setDocumentData(null);
    setError('');
    setStep('camera');
  };

  // Save and continue
  const saveAndContinue = () => {
    if (onCapture && capturedImage) {
      onCapture({
        image: capturedImage,
        text: extractedText,
        documentData,
        timestamp: new Date()
      });
    }
    onClose();
  };

  // Render camera view
  const renderCameraView = () => (
    <div className="relative w-full h-full bg-black">
      <video
        ref={videoRef}
        className="w-full h-full object-cover"
        autoPlay
        playsInline
        muted
      />
      
      {/* Camera overlay */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Document frame guide */}
        <div className="absolute inset-8 border-2 border-white border-dashed rounded-lg opacity-50">
          <div className="absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-white rounded-tl-lg"></div>
          <div className="absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-white rounded-tr-lg"></div>
          <div className="absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-white rounded-bl-lg"></div>
          <div className="absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-white rounded-br-lg"></div>
        </div>
        
        {/* Instructions */}
        <div className="absolute top-8 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-4 py-2 rounded-lg">
          <p className="text-sm text-center">Position document within the frame</p>
        </div>
      </div>
      
      {/* Controls */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex items-center space-x-6">
        {/* Flash toggle */}
        <button
          onClick={toggleFlash}
          className="bg-black bg-opacity-50 text-white p-3 rounded-full"
        >
          {flashEnabled ? <Zap className="w-6 h-6" /> : <ZapOff className="w-6 h-6" />}
        </button>
        
        {/* Capture button */}
        <button
          onClick={handleCapture}
          disabled={isCapturing || !isActive}
          className={`w-16 h-16 rounded-full border-4 border-white flex items-center justify-center ${
            isCapturing ? 'bg-red-500' : 'bg-transparent hover:bg-white hover:bg-opacity-20'
          } transition-all duration-200`}
        >
          {isCapturing ? (
            <Loader2 className="w-8 h-8 text-white animate-spin" />
          ) : (
            <Camera className="w-8 h-8 text-white" />
          )}
        </button>
        
        {/* Camera switch */}
        <button
          onClick={switchCamera}
          className="bg-black bg-opacity-50 text-white p-3 rounded-full"
        >
          <RotateCcw className="w-6 h-6" />
        </button>
      </div>
    </div>
  );

  // Render preview view
  const renderPreviewView = () => (
    <div className="relative w-full h-full bg-gray-100">
      <img
        src={capturedImage?.dataURL}
        alt="Captured document"
        className="w-full h-full object-contain"
      />
      
      {/* Controls */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex items-center space-x-4">
        <button
          onClick={retakePhoto}
          className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg flex items-center space-x-2"
        >
          <RotateCcw className="w-5 h-5" />
          <span>Retake</span>
        </button>
        
        <button
          onClick={() => processImage(capturedImage.blob)}
          disabled={isProcessing}
          className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-3 rounded-lg flex items-center space-x-2"
        >
          {isProcessing ? (
            <Loader2 className="w-5 h-5 animate-spin" />
          ) : (
            <FileText className="w-5 h-5" />
          )}
          <span>{isProcessing ? 'Processing...' : 'Extract Text'}</span>
        </button>
      </div>
    </div>
  );

  // Render processing view
  const renderProcessingView = () => (
    <div className="flex flex-col items-center justify-center h-full bg-gray-50 p-8">
      <div className="bg-white rounded-xl p-8 text-center max-w-sm">
        <Loader2 className="w-12 h-12 text-blue-600 animate-spin mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Processing Document
        </h3>
        <p className="text-gray-600 text-sm">
          Extracting text from your document using OCR technology...
        </p>
      </div>
    </div>
  );

  // Render result view
  const renderResultView = () => (
    <div className="h-full bg-gray-50 p-4 overflow-y-auto">
      <div className="bg-white rounded-xl p-6 mb-4">
        <div className="flex items-center space-x-3 mb-4">
          <CheckCircle className="w-6 h-6 text-green-600" />
          <h3 className="text-lg font-semibold text-gray-900">
            Text Extracted Successfully
          </h3>
        </div>
        
        {documentData && (
          <div className="mb-4 p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Document Type:</strong> {documentData.type}
            </p>
          </div>
        )}
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Extracted Text
            </label>
            <textarea
              value={extractedText}
              onChange={(e) => setExtractedText(e.target.value)}
              className="w-full h-32 p-3 border border-gray-300 rounded-lg resize-none"
              placeholder="Extracted text will appear here..."
            />
          </div>
          
          {documentData?.extractedData && Object.keys(documentData.extractedData).length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Structured Data
              </label>
              <div className="bg-gray-50 rounded-lg p-3">
                {Object.entries(documentData.extractedData).map(([key, value]) => (
                  <div key={key} className="flex justify-between py-1">
                    <span className="text-sm text-gray-600 capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}:
                    </span>
                    <span className="text-sm font-medium text-gray-900">
                      {value}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
        
        <div className="flex space-x-3 mt-6">
          <button
            onClick={retakePhoto}
            className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-3 px-4 rounded-lg flex items-center justify-center space-x-2"
          >
            <RotateCcw className="w-5 h-5" />
            <span>Retake</span>
          </button>
          
          <button
            onClick={saveAndContinue}
            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg flex items-center justify-center space-x-2"
          >
            <Download className="w-5 h-5" />
            <span>Save</span>
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black z-50 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-black text-white">
        <h2 className="text-lg font-semibold">Document Scanner</h2>
        <button
          onClick={onClose}
          className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors duration-200"
        >
          <X className="w-6 h-6" />
        </button>
      </div>
      
      {/* Error message */}
      {error && (
        <div className="bg-red-500 text-white p-3 flex items-center space-x-2">
          <AlertCircle className="w-5 h-5" />
          <span className="text-sm">{error}</span>
        </div>
      )}
      
      {/* Main content */}
      <div className="flex-1 relative">
        {step === 'camera' && renderCameraView()}
        {step === 'preview' && renderPreviewView()}
        {step === 'processing' && renderProcessingView()}
        {step === 'result' && renderResultView()}
      </div>
      
      {/* Add flash animation styles */}
      <style jsx>{`
        @keyframes flash {
          0% { opacity: 0; }
          50% { opacity: 0.8; }
          100% { opacity: 0; }
        }
      `}</style>
    </div>
  );
};

export default CameraScanner;
