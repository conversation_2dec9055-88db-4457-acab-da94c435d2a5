'use client';

import { useState, useEffect } from 'react';
import { X, Download, Smartphone, Monitor, Zap, Wifi } from 'lucide-react';

const PWAInstallPrompt = () => {
  const [deferredPrompt, setDeferredPrompt] = useState(null);
  const [showPrompt, setShowPrompt] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [isStandalone, setIsStandalone] = useState(false);
  const [showIOSInstructions, setShowIOSInstructions] = useState(false);

  useEffect(() => {
    // Check if app is already installed/running in standalone mode
    const checkStandalone = () => {
      return window.matchMedia('(display-mode: standalone)').matches ||
             window.navigator.standalone ||
             document.referrer.includes('android-app://');
    };

    // Detect iOS devices
    const checkIOS = () => {
      return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
    };

    setIsStandalone(checkStandalone());
    setIsIOS(checkIOS());

    // Don't show prompt if already installed
    if (checkStandalone()) {
      setIsInstalled(true);
      return;
    }

    // Listen for the beforeinstallprompt event
    const handleBeforeInstallPrompt = (e) => {
      console.log('[PWA] beforeinstallprompt event fired');
      e.preventDefault();
      setDeferredPrompt(e);
      
      // Show prompt after a delay (better UX)
      setTimeout(() => {
        if (!localStorage.getItem('pwa-install-dismissed')) {
          setShowPrompt(true);
        }
      }, 3000);
    };

    // Listen for app installed event
    const handleAppInstalled = () => {
      console.log('[PWA] App was installed');
      setIsInstalled(true);
      setShowPrompt(false);
      setDeferredPrompt(null);
      
      // Track installation
      if (typeof gtag !== 'undefined') {
        gtag('event', 'pwa_install', {
          event_category: 'PWA',
          event_label: 'App Installed'
        });
      }
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    // For iOS, show custom prompt after some interaction
    if (checkIOS() && !checkStandalone()) {
      const showIOSPrompt = () => {
        if (!localStorage.getItem('ios-install-dismissed')) {
          setTimeout(() => setShowPrompt(true), 5000);
        }
      };
      
      // Show after user interaction
      document.addEventListener('click', showIOSPrompt, { once: true });
    }

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  const handleInstallClick = async () => {
    if (!deferredPrompt && !isIOS) return;

    if (isIOS) {
      setShowIOSInstructions(true);
      return;
    }

    try {
      // Show the install prompt
      deferredPrompt.prompt();
      
      // Wait for the user to respond to the prompt
      const { outcome } = await deferredPrompt.userChoice;
      
      console.log(`[PWA] User response to install prompt: ${outcome}`);
      
      if (outcome === 'accepted') {
        console.log('[PWA] User accepted the install prompt');
        // Track acceptance
        if (typeof gtag !== 'undefined') {
          gtag('event', 'pwa_install_accepted', {
            event_category: 'PWA',
            event_label: 'Install Accepted'
          });
        }
      } else {
        console.log('[PWA] User dismissed the install prompt');
        // Track dismissal
        if (typeof gtag !== 'undefined') {
          gtag('event', 'pwa_install_dismissed', {
            event_category: 'PWA',
            event_label: 'Install Dismissed'
          });
        }
      }
      
      // Clear the deferredPrompt
      setDeferredPrompt(null);
      setShowPrompt(false);
      
    } catch (error) {
      console.error('[PWA] Error showing install prompt:', error);
    }
  };

  const handleDismiss = () => {
    setShowPrompt(false);
    localStorage.setItem(isIOS ? 'ios-install-dismissed' : 'pwa-install-dismissed', 'true');
    
    // Track dismissal
    if (typeof gtag !== 'undefined') {
      gtag('event', 'pwa_prompt_dismissed', {
        event_category: 'PWA',
        event_label: isIOS ? 'iOS Prompt Dismissed' : 'Android Prompt Dismissed'
      });
    }
  };

  const handleRemindLater = () => {
    setShowPrompt(false);
    // Set reminder for 7 days
    const reminderDate = new Date();
    reminderDate.setDate(reminderDate.getDate() + 7);
    localStorage.setItem('pwa-remind-later', reminderDate.toISOString());
  };

  // Don't render if installed or shouldn't show
  if (isInstalled || !showPrompt) return null;

  // iOS Instructions Modal
  if (showIOSInstructions) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-2xl p-6 max-w-sm w-full">
          <div className="text-center mb-4">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
              <Smartphone className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">
              Install BEC Compare
            </h3>
            <p className="text-gray-600 text-sm">
              Add to your home screen for quick access
            </p>
          </div>

          <div className="space-y-4 mb-6">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">
                1
              </div>
              <p className="text-sm text-gray-700">
                Tap the <strong>Share</strong> button at the bottom of your screen
              </p>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">
                2
              </div>
              <p className="text-sm text-gray-700">
                Scroll down and tap <strong>"Add to Home Screen"</strong>
              </p>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">
                3
              </div>
              <p className="text-sm text-gray-700">
                Tap <strong>"Add"</strong> to confirm
              </p>
            </div>
          </div>

          <button
            onClick={() => setShowIOSInstructions(false)}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
          >
            Got it!
          </button>
        </div>
      </div>
    );
  }

  // Main Install Prompt
  return (
    <div className="fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:max-w-sm bg-white rounded-2xl shadow-2xl border border-gray-200 p-6 z-40 transform transition-all duration-300 ease-out">
      {/* Close Button */}
      <button
        onClick={handleDismiss}
        className="absolute top-3 right-3 p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200"
        aria-label="Dismiss install prompt"
      >
        <X className="w-5 h-5" />
      </button>

      {/* Content */}
      <div className="pr-8">
        <div className="flex items-center space-x-3 mb-4">
          <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
            <Download className="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <h3 className="font-bold text-gray-900 text-lg">
              Install BEC Compare
            </h3>
            <p className="text-gray-600 text-sm">
              Get the full app experience
            </p>
          </div>
        </div>

        {/* Benefits */}
        <div className="space-y-2 mb-6">
          <div className="flex items-center space-x-2 text-sm text-gray-700">
            <Zap className="w-4 h-4 text-green-600" />
            <span>Faster loading and better performance</span>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-700">
            <Wifi className="w-4 h-4 text-blue-600" />
            <span>Works offline with cached data</span>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-700">
            <Monitor className="w-4 h-4 text-purple-600" />
            <span>Native app-like experience</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <button
            onClick={handleInstallClick}
            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2.5 px-4 rounded-lg transition-colors duration-200 text-sm"
          >
            {isIOS ? 'Show Instructions' : 'Install App'}
          </button>
          <button
            onClick={handleRemindLater}
            className="px-4 py-2.5 text-gray-600 hover:text-gray-800 font-medium text-sm transition-colors duration-200"
          >
            Later
          </button>
        </div>
      </div>
    </div>
  );
};

export default PWAInstallPrompt;
