'use client';

import { useState, useEffect, useRef } from 'react';
import {
  MapPin,
  Navigation,
  Route,
  Clock,
  Car,
  PersonStanding,
  Bike,
  Bus,
  Locate,
  Layers,
  Search,
  Filter
} from 'lucide-react';
import { enhancedLocationService } from '../../lib/geoService';

const OpenStreetMapIntegration = ({ 
  colleges = [], 
  selectedCollege = null,
  onCollegeSelect,
  showRoute = false,
  className = ''
}) => {
  const mapRef = useRef(null);
  const mapInstanceRef = useRef(null);
  const markersRef = useRef([]);
  const routeLayerRef = useRef(null);
  
  const [isLoaded, setIsLoaded] = useState(false);
  const [userLocation, setUserLocation] = useState(null);
  const [mapType, setMapType] = useState('standard');
  const [travelMode, setTravelMode] = useState('driving');
  const [routeInfo, setRouteInfo] = useState(null);
  const [isLoadingRoute, setIsLoadingRoute] = useState(false);
  const [nearbyColleges, setNearbyColleges] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Travel modes
  const travelModes = [
    { value: 'driving', label: 'Drive', icon: Car },
    { value: 'walking', label: 'Walk', icon: PersonStanding },
    { value: 'cycling', label: 'Bike', icon: Bike },
    { value: 'transit', label: 'Transit', icon: Bus }
  ];

  // Map types
  const mapTypes = [
    { value: 'standard', label: 'Standard' },
    { value: 'satellite', label: 'Satellite' },
    { value: 'terrain', label: 'Terrain' }
  ];

  useEffect(() => {
    loadLeafletMap();
    startLocationTracking();
    
    return () => {
      stopLocationTracking();
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
      }
    };
  }, []);

  useEffect(() => {
    if (isLoaded && colleges.length > 0) {
      updateMapMarkers();
    }
  }, [isLoaded, colleges, userLocation]);

  useEffect(() => {
    if (isLoaded && selectedCollege && showRoute && userLocation) {
      calculateRoute();
    }
  }, [isLoaded, selectedCollege, showRoute, userLocation, travelMode]);

  // Load Leaflet map
  const loadLeafletMap = async () => {
    if (typeof window === 'undefined') return;

    try {
      // Dynamic import of Leaflet
      const L = (await import('leaflet')).default;
      
      // Fix for default markers
      delete L.Icon.Default.prototype._getIconUrl;
      L.Icon.Default.mergeOptions({
        iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
        iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
        shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
      });

      initializeMap(L);
    } catch (error) {
      console.error('Failed to load Leaflet:', error);
    }
  };

  // Initialize map
  const initializeMap = (L) => {
    if (!mapRef.current) return;

    const map = L.map(mapRef.current, {
      center: [12.9716, 77.5946], // Bangalore center
      zoom: 12,
      zoomControl: true,
      attributionControl: true
    });

    // Add tile layer based on map type
    const getTileLayer = (type) => {
      switch (type) {
        case 'satellite':
          return L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
            attribution: '© Esri, Maxar, GeoEye, Earthstar Geographics, CNES/Airbus DS, USDA, USGS, AeroGRID, IGN, and the GIS User Community'
          });
        case 'terrain':
          return L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenTopoMap (CC-BY-SA)'
          });
        default:
          return L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
          });
      }
    };

    getTileLayer(mapType).addTo(map);

    mapInstanceRef.current = map;
    setIsLoaded(true);
  };

  // Start location tracking
  const startLocationTracking = () => {
    const unwatch = enhancedLocationService.startWatching((position, error) => {
      if (error) {
        console.error('Location tracking error:', error);
        return;
      }

      const location = {
        lat: position.coords.latitude,
        lng: position.coords.longitude
      };

      setUserLocation(location);
      updateNearbyColleges(location);
    });

    return unwatch;
  };

  // Stop location tracking
  const stopLocationTracking = () => {
    enhancedLocationService.stopWatching();
  };

  // Update nearby colleges
  const updateNearbyColleges = (userLoc) => {
    const nearby = colleges.filter(college => {
      const distance = calculateDistance(userLoc, {
        lat: college.latitude,
        lng: college.longitude
      });
      return distance <= 50; // Within 50km
    }).sort((a, b) => {
      const distA = calculateDistance(userLoc, { lat: a.latitude, lng: a.longitude });
      const distB = calculateDistance(userLoc, { lat: b.latitude, lng: b.longitude });
      return distA - distB;
    });

    setNearbyColleges(nearby);
  };

  // Calculate distance between two points
  const calculateDistance = (point1, point2) => {
    const R = 6371; // Earth's radius in km
    const dLat = (point2.lat - point1.lat) * Math.PI / 180;
    const dLon = (point2.lng - point1.lng) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(point1.lat * Math.PI / 180) * Math.cos(point2.lat * Math.PI / 180) *
              Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  // Update map markers
  const updateMapMarkers = async () => {
    if (!mapInstanceRef.current) return;

    const L = (await import('leaflet')).default;

    // Clear existing markers
    markersRef.current.forEach(marker => {
      mapInstanceRef.current.removeLayer(marker);
    });
    markersRef.current = [];

    // Add user location marker
    if (userLocation) {
      const userIcon = L.divIcon({
        html: `<div style="background: #2563eb; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>`,
        iconSize: [20, 20],
        iconAnchor: [10, 10],
        className: 'user-location-marker'
      });

      const userMarker = L.marker([userLocation.lat, userLocation.lng], { icon: userIcon })
        .addTo(mapInstanceRef.current)
        .bindPopup('Your Location');
      
      markersRef.current.push(userMarker);
    }

    // Add college markers
    colleges.forEach(college => {
      const collegeIcon = L.divIcon({
        html: `<div style="background: #dc2626; width: 30px; height: 30px; border-radius: 50% 50% 50% 0; transform: rotate(-45deg); border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3); display: flex; align-items: center; justify-content: center;">
          <div style="transform: rotate(45deg); color: white; font-size: 12px; font-weight: bold;">🏛️</div>
        </div>`,
        iconSize: [30, 30],
        iconAnchor: [15, 30],
        className: 'college-marker'
      });

      const marker = L.marker([college.latitude, college.longitude], { icon: collegeIcon })
        .addTo(mapInstanceRef.current)
        .bindPopup(`
          <div style="min-width: 200px;">
            <h3 style="margin: 0 0 8px 0; font-weight: bold; color: #1f2937;">${college.name}</h3>
            <p style="margin: 4px 0; color: #6b7280; font-size: 14px;">Rank: #${college.rank}</p>
            <p style="margin: 4px 0; color: #6b7280; font-size: 14px;">Placement: ${college.placementRate}%</p>
            <button 
              onclick="window.selectCollege('${college.id}')"
              style="background: #2563eb; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 12px; cursor: pointer; margin-top: 8px;"
            >
              View Details
            </button>
          </div>
        `);

      marker.on('click', () => {
        if (onCollegeSelect) {
          onCollegeSelect(college);
        }
      });

      markersRef.current.push(marker);
    });

    // Fit map to show all markers
    if (markersRef.current.length > 0) {
      const group = new L.featureGroup(markersRef.current);
      mapInstanceRef.current.fitBounds(group.getBounds().pad(0.1));
    }
  };

  // Calculate route using OpenRouteService (free alternative)
  const calculateRoute = async () => {
    if (!userLocation || !selectedCollege) return;

    setIsLoadingRoute(true);

    try {
      // Use a simple routing service or calculate straight-line distance
      const distance = calculateDistance(userLocation, {
        lat: selectedCollege.latitude,
        lng: selectedCollege.longitude
      });

      // Estimate travel time based on mode
      const speeds = {
        driving: 40, // km/h
        walking: 5,  // km/h
        cycling: 15, // km/h
        transit: 25  // km/h
      };

      const speed = speeds[travelMode] || speeds.driving;
      const timeInHours = distance / speed;
      const timeInMinutes = Math.round(timeInHours * 60);

      setRouteInfo({
        distance: `${distance.toFixed(1)} km`,
        duration: timeInMinutes < 60 
          ? `${timeInMinutes} min` 
          : `${Math.floor(timeInMinutes / 60)}h ${timeInMinutes % 60}m`,
        steps: Math.ceil(distance / 5) // Rough estimate
      });

      // Draw simple line route
      await drawSimpleRoute();

    } catch (error) {
      console.error('Error calculating route:', error);
    } finally {
      setIsLoadingRoute(false);
    }
  };

  // Draw simple route line
  const drawSimpleRoute = async () => {
    if (!mapInstanceRef.current || !userLocation || !selectedCollege) return;

    const L = (await import('leaflet')).default;

    // Remove existing route
    if (routeLayerRef.current) {
      mapInstanceRef.current.removeLayer(routeLayerRef.current);
    }

    // Draw simple line
    const routeLine = L.polyline([
      [userLocation.lat, userLocation.lng],
      [selectedCollege.latitude, selectedCollege.longitude]
    ], {
      color: '#2563eb',
      weight: 4,
      opacity: 0.8,
      dashArray: '10, 10'
    }).addTo(mapInstanceRef.current);

    routeLayerRef.current = routeLine;

    // Fit bounds to show route
    mapInstanceRef.current.fitBounds(routeLine.getBounds().pad(0.1));
  };

  // Center map on user location
  const centerOnUser = () => {
    if (userLocation && mapInstanceRef.current) {
      mapInstanceRef.current.setView([userLocation.lat, userLocation.lng], 15);
    }
  };

  // Change map type
  const changeMapType = async (type) => {
    setMapType(type);
    if (mapInstanceRef.current) {
      const L = (await import('leaflet')).default;
      
      // Remove current tile layer
      mapInstanceRef.current.eachLayer((layer) => {
        if (layer instanceof L.TileLayer) {
          mapInstanceRef.current.removeLayer(layer);
        }
      });

      // Add new tile layer
      const getTileLayer = (mapType) => {
        switch (mapType) {
          case 'satellite':
            return L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
              attribution: '© Esri, Maxar, GeoEye, Earthstar Geographics, CNES/Airbus DS, USDA, USGS, AeroGRID, IGN, and the GIS User Community'
            });
          case 'terrain':
            return L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
              attribution: '© OpenTopoMap (CC-BY-SA)'
            });
          default:
            return L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
              attribution: '© OpenStreetMap contributors'
            });
        }
      };

      getTileLayer(type).addTo(mapInstanceRef.current);
    }
  };

  // Global function for popup buttons
  useEffect(() => {
    window.selectCollege = (collegeId) => {
      const college = colleges.find(c => c.id === collegeId);
      if (college && onCollegeSelect) {
        onCollegeSelect(college);
      }
    };

    return () => {
      delete window.selectCollege;
    };
  }, [colleges, onCollegeSelect]);

  return (
    <>
      {/* Leaflet CSS */}
      <link
        rel="stylesheet"
        href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css"
        integrity="sha512-xodZBNTC5n17Xt2atTPuE1HxjVMSvLVW9ocqUKLsCC5CXdbqCmblAshOMAS6/keqq/sMZMZ19scR4PsZChSR7A=="
        crossOrigin=""
      />
      
      <div className={`relative bg-white rounded-xl shadow-lg overflow-hidden ${className}`}>
        {/* Map Container */}
        <div ref={mapRef} className="w-full h-96 md:h-[500px]" />

        {/* Map Controls */}
        <div className="absolute top-4 left-4 space-y-2">
          {/* Location button */}
          <button
            onClick={centerOnUser}
            className="bg-white rounded-lg p-2 shadow-lg hover:shadow-xl transition-shadow duration-200"
            title="Center on my location"
          >
            <Locate className="w-5 h-5 text-gray-600" />
          </button>

          {/* Map type selector */}
          <div className="relative">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="bg-white rounded-lg p-2 shadow-lg hover:shadow-xl transition-shadow duration-200"
              title="Map options"
            >
              <Layers className="w-5 h-5 text-gray-600" />
            </button>

            {showFilters && (
              <div className="absolute top-12 left-0 bg-white rounded-lg shadow-xl border border-gray-200 p-2 min-w-[120px] z-10">
                <div className="space-y-1">
                  {mapTypes.map(type => (
                    <button
                      key={type.value}
                      onClick={() => {
                        changeMapType(type.value);
                        setShowFilters(false);
                      }}
                      className={`w-full text-left px-3 py-2 rounded text-sm transition-colors duration-200 ${
                        mapType === type.value 
                          ? 'bg-blue-100 text-blue-700' 
                          : 'hover:bg-gray-100'
                      }`}
                    >
                      {type.label}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Travel Mode Selector */}
        {showRoute && (
          <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg p-2">
            <div className="flex space-x-1">
              {travelModes.map(mode => {
                const Icon = mode.icon;
                return (
                  <button
                    key={mode.value}
                    onClick={() => setTravelMode(mode.value)}
                    className={`p-2 rounded transition-colors duration-200 ${
                      travelMode === mode.value 
                        ? 'bg-blue-100 text-blue-700' 
                        : 'hover:bg-gray-100 text-gray-600'
                    }`}
                    title={mode.label}
                  >
                    <Icon className="w-4 h-4" />
                  </button>
                );
              })}
            </div>
          </div>
        )}

        {/* Route Information */}
        {routeInfo && (
          <div className="absolute bottom-4 left-4 right-4 bg-white rounded-lg shadow-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Route className="w-5 h-5 text-blue-600" />
                  <span className="font-medium text-gray-900">{routeInfo.distance}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="w-5 h-5 text-green-600" />
                  <span className="font-medium text-gray-900">{routeInfo.duration}</span>
                </div>
              </div>
              
              <button
                onClick={() => {
                  const url = `https://www.openstreetmap.org/directions?from=${userLocation.lat},${userLocation.lng}&to=${selectedCollege.latitude},${selectedCollege.longitude}&route=${travelMode}`;
                  window.open(url, '_blank');
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm flex items-center space-x-2"
              >
                <Navigation className="w-4 h-4" />
                <span>Navigate</span>
              </button>
            </div>
          </div>
        )}

        {/* Loading indicator */}
        {isLoadingRoute && (
          <div className="absolute inset-0 bg-black bg-opacity-25 flex items-center justify-center">
            <div className="bg-white rounded-lg p-4 flex items-center space-x-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
              <span className="text-sm text-gray-700">Calculating route...</span>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default OpenStreetMapIntegration;
