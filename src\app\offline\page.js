'use client';

import { useState, useEffect } from 'react';
import { Wifi, WifiOff, RefreshCw, Home, Search, BookOpen } from 'lucide-react';

export default function OfflinePage() {
  const [isOnline, setIsOnline] = useState(true);
  const [cachedColleges, setCachedColleges] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Check online status
    setIsOnline(navigator.onLine);

    // Listen for online/offline events
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Load cached college data
    loadCachedData();

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const loadCachedData = async () => {
    try {
      // Try to get cached college data
      if ('caches' in window) {
        const cache = await caches.open('bec-api-v2.0.0');
        const cachedResponse = await cache.match('/colleges.json');
        
        if (cachedResponse) {
          const data = await cachedResponse.json();
          setCachedColleges(data.slice(0, 10)); // Show first 10 colleges
        }
      }
    } catch (error) {
      console.error('Failed to load cached data:', error);
    }
  };

  const handleRetry = async () => {
    setIsLoading(true);
    
    try {
      // Try to fetch fresh data
      const response = await fetch('/colleges.json');
      if (response.ok) {
        window.location.href = '/';
      } else {
        throw new Error('Network request failed');
      }
    } catch (error) {
      console.error('Retry failed:', error);
      // Show user that they're still offline
      setTimeout(() => setIsLoading(false), 1000);
    }
  };

  const navigateToHome = () => {
    window.location.href = '/';
  };

  const navigateToColleges = () => {
    window.location.href = '/colleges';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center">
        {/* Status Icon */}
        <div className="mb-6">
          {isOnline ? (
            <div className="inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full">
              <Wifi className="w-10 h-10 text-green-600" />
            </div>
          ) : (
            <div className="inline-flex items-center justify-center w-20 h-20 bg-red-100 rounded-full">
              <WifiOff className="w-10 h-10 text-red-600" />
            </div>
          )}
        </div>

        {/* Status Message */}
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          {isOnline ? 'Connection Restored!' : 'You\'re Offline'}
        </h1>
        
        <p className="text-gray-600 mb-6">
          {isOnline 
            ? 'Great! Your internet connection is back. You can now access all features.'
            : 'No internet connection detected. You can still browse cached college data below.'
          }
        </p>

        {/* Action Buttons */}
        <div className="space-y-3 mb-8">
          <button
            onClick={handleRetry}
            disabled={isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
          >
            <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
            <span>{isLoading ? 'Checking...' : 'Try Again'}</span>
          </button>

          <div className="flex space-x-3">
            <button
              onClick={navigateToHome}
              className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
            >
              <Home className="w-4 h-4" />
              <span>Home</span>
            </button>
            
            <button
              onClick={navigateToColleges}
              className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
            >
              <Search className="w-4 h-4" />
              <span>Browse</span>
            </button>
          </div>
        </div>

        {/* Cached Data Preview */}
        {cachedColleges.length > 0 && (
          <div className="border-t pt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center justify-center space-x-2">
              <BookOpen className="w-5 h-5" />
              <span>Cached Colleges</span>
            </h3>
            
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {cachedColleges.map((college, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer"
                  onClick={() => window.location.href = `/colleges/${college.id}`}
                >
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <span className="text-blue-600 font-semibold text-sm">
                      {college.name?.charAt(0) || 'C'}
                    </span>
                  </div>
                  <div className="flex-1 text-left">
                    <p className="font-medium text-gray-900 text-sm truncate">
                      {college.name || 'College Name'}
                    </p>
                    <p className="text-gray-500 text-xs">
                      Rank: {college.rank || 'N/A'}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            
            <p className="text-xs text-gray-500 mt-3">
              Showing cached data. Connect to internet for latest information.
            </p>
          </div>
        )}

        {/* PWA Info */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>💡 Tip:</strong> Add this app to your home screen for quick access and better offline experience!
          </p>
        </div>
      </div>
    </div>
  );
}
