'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from './useAuth';
import { 
  addToFavorites, 
  removeFromFavorites, 
  getUserFavorites, 
  isFavorite 
} from '../lib/userService';

export const useFavorites = () => {
  const { user } = useAuth();
  const [favorites, setFavorites] = useState([]);
  const [favoriteIds, setFavoriteIds] = useState(new Set());
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load user favorites
  const loadFavorites = useCallback(async () => {
    if (!user) {
      setFavorites([]);
      setFavoriteIds(new Set());
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const userFavorites = await getUserFavorites(user.uid);
      setFavorites(userFavorites);
      setFavoriteIds(new Set(userFavorites.map(fav => fav.collegeId)));
    } catch (err) {
      setError(err.message);
      console.error('Error loading favorites:', err);
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Add college to favorites
  const addFavorite = useCallback(async (collegeId) => {
    if (!user) {
      setError('Please sign in to add favorites');
      return false;
    }

    try {
      setError(null);
      await addToFavorites(user.uid, collegeId);
      
      // Update local state
      const newFavorite = {
        id: `${user.uid}_${collegeId}`,
        userId: user.uid,
        collegeId,
        createdAt: new Date()
      };
      
      setFavorites(prev => [newFavorite, ...prev]);
      setFavoriteIds(prev => new Set([...prev, collegeId]));
      
      return true;
    } catch (err) {
      setError(err.message);
      console.error('Error adding favorite:', err);
      return false;
    }
  }, [user]);

  // Remove college from favorites
  const removeFavorite = useCallback(async (collegeId) => {
    if (!user) {
      setError('Please sign in to manage favorites');
      return false;
    }

    try {
      setError(null);
      await removeFromFavorites(user.uid, collegeId);
      
      // Update local state
      setFavorites(prev => prev.filter(fav => fav.collegeId !== collegeId));
      setFavoriteIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(collegeId);
        return newSet;
      });
      
      return true;
    } catch (err) {
      setError(err.message);
      console.error('Error removing favorite:', err);
      return false;
    }
  }, [user]);

  // Toggle favorite status
  const toggleFavorite = useCallback(async (collegeId) => {
    if (!user) {
      setError('Please sign in to manage favorites');
      return false;
    }

    const isCurrentlyFavorite = favoriteIds.has(collegeId);
    
    if (isCurrentlyFavorite) {
      return await removeFavorite(collegeId);
    } else {
      return await addFavorite(collegeId);
    }
  }, [user, favoriteIds, addFavorite, removeFavorite]);

  // Check if college is favorite
  const checkIsFavorite = useCallback((collegeId) => {
    return favoriteIds.has(collegeId);
  }, [favoriteIds]);

  // Load favorites when user changes
  useEffect(() => {
    loadFavorites();
  }, [loadFavorites]);

  return {
    favorites,
    favoriteIds,
    loading,
    error,
    addFavorite,
    removeFavorite,
    toggleFavorite,
    checkIsFavorite,
    refreshFavorites: loadFavorites,
    setError
  };
};

export default useFavorites;
