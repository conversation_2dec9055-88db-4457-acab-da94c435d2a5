// Natural Language Processing Service for Smart Search
import nlp from 'compromise';

// Intent patterns for different types of queries
const INTENT_PATTERNS = {
  location: {
    keywords: ['near', 'in', 'around', 'close to', 'proximity', 'distance'],
    entities: ['bangalore', 'bengaluru', 'mysore', 'mysuru', 'electronic city', 'whitefield', 'koramangala', 'indiranagar', 'jp nagar', 'banashankari', 'yelahanka', 'hebbal']
  },
  placement: {
    keywords: ['placement', 'job', 'career', 'salary', 'package', 'companies', 'recruiting', 'hiring'],
    modifiers: ['high', 'good', 'best', 'top', 'excellent', 'above', 'over', 'minimum']
  },
  courses: {
    keywords: ['engineering', 'computer science', 'cse', 'ece', 'mechanical', 'civil', 'electrical', 'biotechnology', 'ai', 'ml', 'data science', 'cyber security'],
    programs: ['b.e', 'b.tech', 'm.tech', 'mba', 'mca', 'phd']
  },
  ranking: {
    keywords: ['rank', 'ranking', 'top', 'best', 'nirf', 'rated', 'prestigious'],
    modifiers: ['top 10', 'top 20', 'best', 'highest rated']
  },
  infrastructure: {
    keywords: ['campus', 'facilities', 'hostel', 'library', 'lab', 'sports', 'wifi', 'metro', 'transport'],
    features: ['metro access', 'bus connectivity', 'large campus', 'modern facilities']
  },
  fees: {
    keywords: ['fee', 'cost', 'tuition', 'affordable', 'cheap', 'expensive', 'budget'],
    modifiers: ['low', 'high', 'under', 'below', 'above']
  }
};

// Extract entities and intent from natural language query
export const parseQuery = (query) => {
  if (!query || typeof query !== 'string') {
    return {
      intent: 'general',
      entities: {},
      filters: {},
      searchTerms: []
    };
  }

  const doc = nlp(query.toLowerCase());
  const result = {
    intent: 'general',
    entities: {},
    filters: {},
    searchTerms: [],
    confidence: 0
  };

  // Extract numbers for numerical filters
  const numbers = doc.numbers().out('array');
  
  // Extract places/locations
  const places = doc.places().out('array');
  
  // Extract organizations (college names/acronyms)
  const orgs = doc.organizations().out('array');

  // Analyze intent based on keywords
  let maxIntentScore = 0;
  let primaryIntent = 'general';

  Object.entries(INTENT_PATTERNS).forEach(([intent, patterns]) => {
    let score = 0;
    patterns.keywords.forEach(keyword => {
      if (query.toLowerCase().includes(keyword)) {
        score += 2;
      }
    });
    
    if (patterns.entities) {
      patterns.entities.forEach(entity => {
        if (query.toLowerCase().includes(entity)) {
          score += 1;
        }
      });
    }

    if (score > maxIntentScore) {
      maxIntentScore = score;
      primaryIntent = intent;
    }
  });

  result.intent = primaryIntent;
  result.confidence = Math.min(maxIntentScore / 5, 1); // Normalize to 0-1

  // Extract specific entities based on intent
  switch (primaryIntent) {
    case 'location':
      result.entities.locations = [...places, ...extractLocationEntities(query)];
      break;
      
    case 'placement':
      result.entities.companies = extractCompanyNames(query);
      result.entities.packages = extractPackageInfo(query, numbers);
      result.filters.placementFocus = true;
      break;
      
    case 'courses':
      result.entities.courses = extractCourseNames(query);
      result.entities.programs = extractProgramTypes(query);
      break;
      
    case 'ranking':
      result.entities.rankings = extractRankingInfo(query, numbers);
      result.filters.rankingFocus = true;
      break;
      
    case 'infrastructure':
      result.entities.facilities = extractFacilities(query);
      result.filters.infrastructureFocus = true;
      break;
      
    case 'fees':
      result.entities.feeRange = extractFeeRange(query, numbers);
      result.filters.feeFocus = true;
      break;
  }

  // Extract college names and acronyms
  result.entities.colleges = [...orgs, ...extractCollegeNames(query)];
  
  // Extract general search terms (remove common words)
  const commonWords = ['the', 'and', 'or', 'in', 'at', 'with', 'for', 'of', 'to', 'a', 'an'];
  result.searchTerms = query.toLowerCase()
    .split(/\s+/)
    .filter(word => word.length > 2 && !commonWords.includes(word));

  return result;
};

// Helper functions for entity extraction
const extractLocationEntities = (query) => {
  const locations = [];
  INTENT_PATTERNS.location.entities.forEach(location => {
    if (query.toLowerCase().includes(location)) {
      locations.push(location);
    }
  });
  return locations;
};

const extractCompanyNames = (query) => {
  const companies = ['microsoft', 'google', 'amazon', 'tcs', 'infosys', 'wipro', 'accenture', 'ibm', 'cisco', 'intel', 'dell', 'hp', 'oracle', 'sap', 'adobe'];
  return companies.filter(company => query.toLowerCase().includes(company));
};

const extractPackageInfo = (query, numbers) => {
  const packageInfo = {};
  
  // Look for package-related numbers
  numbers.forEach(num => {
    const numValue = parseFloat(num);
    if (numValue > 0 && numValue < 200) { // Reasonable package range in LPA
      if (query.includes('above') || query.includes('over') || query.includes('>')) {
        packageInfo.min = numValue;
      } else if (query.includes('below') || query.includes('under') || query.includes('<')) {
        packageInfo.max = numValue;
      } else {
        packageInfo.target = numValue;
      }
    }
  });
  
  return packageInfo;
};

const extractCourseNames = (query) => {
  const courses = [];
  INTENT_PATTERNS.courses.keywords.forEach(course => {
    if (query.toLowerCase().includes(course)) {
      courses.push(course);
    }
  });
  return courses;
};

const extractProgramTypes = (query) => {
  const programs = [];
  INTENT_PATTERNS.courses.programs.forEach(program => {
    if (query.toLowerCase().includes(program)) {
      programs.push(program);
    }
  });
  return programs;
};

const extractRankingInfo = (query, numbers) => {
  const rankingInfo = {};
  
  numbers.forEach(num => {
    const numValue = parseInt(num);
    if (numValue > 0 && numValue <= 500) { // Reasonable ranking range
      if (query.includes('top') || query.includes('within')) {
        rankingInfo.maxRank = numValue;
      } else {
        rankingInfo.targetRank = numValue;
      }
    }
  });
  
  return rankingInfo;
};

const extractFacilities = (query) => {
  const facilities = [];
  INTENT_PATTERNS.infrastructure.keywords.forEach(facility => {
    if (query.toLowerCase().includes(facility)) {
      facilities.push(facility);
    }
  });
  return facilities;
};

const extractFeeRange = (query, numbers) => {
  const feeInfo = {};
  
  numbers.forEach(num => {
    const numValue = parseFloat(num);
    if (numValue > 1000 && numValue < 10000000) { // Reasonable fee range
      if (query.includes('under') || query.includes('below') || query.includes('<')) {
        feeInfo.max = numValue;
      } else if (query.includes('above') || query.includes('over') || query.includes('>')) {
        feeInfo.min = numValue;
      }
    }
  });
  
  return feeInfo;
};

const extractCollegeNames = (query) => {
  const collegeAcronyms = ['rvce', 'rvitm', 'rvu', 'pesurrc', 'bmsce', 'msrit', 'sir mvit', 'bit', 'nmit', 'pesuecc', 'cmrit', 'dsce', 'bmsit', 'reva', 'msruas'];
  return collegeAcronyms.filter(acronym => query.toLowerCase().includes(acronym));
};

// Generate search suggestions based on partial query
export const generateSearchSuggestions = (partialQuery) => {
  if (!partialQuery || partialQuery.length < 2) {
    return [];
  }

  const suggestions = [
    // Location-based suggestions
    `${partialQuery} near Electronic City`,
    `${partialQuery} in Bangalore`,
    `${partialQuery} with metro access`,
    
    // Placement-focused suggestions
    `${partialQuery} with good placements`,
    `${partialQuery} high package colleges`,
    `${partialQuery} top recruiting companies`,
    
    // Course-specific suggestions
    `${partialQuery} computer science engineering`,
    `${partialQuery} artificial intelligence`,
    `${partialQuery} data science programs`,
    
    // Ranking suggestions
    `${partialQuery} top ranked colleges`,
    `${partialQuery} NIRF ranked`,
    `${partialQuery} best engineering colleges`
  ];

  // Filter suggestions that make sense with the partial query
  return suggestions
    .filter(suggestion => suggestion.toLowerCase().includes(partialQuery.toLowerCase()))
    .slice(0, 5);
};

// Convert parsed query to search filters
export const queryToFilters = (parsedQuery) => {
  const filters = { ...parsedQuery.filters };
  
  // Convert entities to specific filters
  if (parsedQuery.entities.packages) {
    const pkg = parsedQuery.entities.packages;
    if (pkg.min) filters.minPackage = pkg.min;
    if (pkg.max) filters.maxPackage = pkg.max;
  }
  
  if (parsedQuery.entities.rankings) {
    const rank = parsedQuery.entities.rankings;
    if (rank.maxRank) filters.maxRanking = rank.maxRank;
  }
  
  if (parsedQuery.entities.facilities) {
    if (parsedQuery.entities.facilities.includes('metro')) {
      filters.metroAccess = true;
    }
  }
  
  if (parsedQuery.entities.locations) {
    filters.preferredLocations = parsedQuery.entities.locations;
  }
  
  return filters;
};

export default {
  parseQuery,
  generateSearchSuggestions,
  queryToFilters
};
