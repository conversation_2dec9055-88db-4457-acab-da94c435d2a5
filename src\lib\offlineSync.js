/**
 * Offline Synchronization Utility
 * Handles syncing user preferences, search history, and analytics data
 * when the connection is restored after being offline
 */

import { db } from './firebase';
import { doc, setDoc, getDoc, updateDoc, arrayUnion, serverTimestamp } from 'firebase/firestore';

// Local storage keys for offline data
const STORAGE_KEYS = {
  USER_PREFERENCES: 'bec_user_preferences',
  SEARCH_HISTORY: 'bec_search_history',
  ANALYTICS_QUEUE: 'bec_analytics_queue',
  COMPARISON_HISTORY: 'bec_comparison_history',
  FAVORITES: 'bec_favorites',
  SYNC_QUEUE: 'bec_sync_queue'
};

// Maximum items to store locally
const MAX_LOCAL_ITEMS = {
  searchHistory: 100,
  analyticsQueue: 500,
  comparisonHistory: 50
};

class OfflineSyncManager {
  constructor() {
    this.isOnline = navigator.onLine;
    this.syncInProgress = false;
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Listen for online/offline events
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.handleConnectionRestored();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      console.log('[OfflineSync] Connection lost - switching to offline mode');
    });

    // Listen for service worker messages
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data.type === 'BACKGROUND_SYNC') {
          this.handleBackgroundSync();
        }
      });
    }
  }

  // Store data locally when offline
  storeOfflineData(key, data) {
    try {
      const existingData = this.getOfflineData(key) || [];
      const updatedData = Array.isArray(existingData) ? [...existingData, data] : [data];
      
      // Limit storage size
      const maxItems = MAX_LOCAL_ITEMS[key] || 100;
      if (updatedData.length > maxItems) {
        updatedData.splice(0, updatedData.length - maxItems);
      }

      localStorage.setItem(STORAGE_KEYS[key.toUpperCase()], JSON.stringify(updatedData));
      console.log(`[OfflineSync] Stored ${key} data locally:`, data);
    } catch (error) {
      console.error(`[OfflineSync] Failed to store ${key} data:`, error);
    }
  }

  // Retrieve data from local storage
  getOfflineData(key) {
    try {
      const data = localStorage.getItem(STORAGE_KEYS[key.toUpperCase()]);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error(`[OfflineSync] Failed to retrieve ${key} data:`, error);
      return null;
    }
  }

  // Clear local data after successful sync
  clearOfflineData(key) {
    try {
      localStorage.removeItem(STORAGE_KEYS[key.toUpperCase()]);
      console.log(`[OfflineSync] Cleared ${key} data from local storage`);
    } catch (error) {
      console.error(`[OfflineSync] Failed to clear ${key} data:`, error);
    }
  }

  // Store user preferences offline
  async storeUserPreferences(userId, preferences) {
    if (this.isOnline) {
      try {
        await this.syncUserPreferences(userId, preferences);
      } catch (error) {
        console.warn('[OfflineSync] Failed to sync preferences online, storing offline');
        this.storeOfflineData('userPreferences', { userId, preferences, timestamp: Date.now() });
      }
    } else {
      this.storeOfflineData('userPreferences', { userId, preferences, timestamp: Date.now() });
    }
  }

  // Store search history offline
  async storeSearchHistory(userId, searchData) {
    const searchEntry = {
      userId,
      query: searchData.query,
      filters: searchData.filters,
      results: searchData.results?.length || 0,
      timestamp: Date.now()
    };

    if (this.isOnline) {
      try {
        await this.syncSearchHistory(userId, searchEntry);
      } catch (error) {
        console.warn('[OfflineSync] Failed to sync search online, storing offline');
        this.storeOfflineData('searchHistory', searchEntry);
      }
    } else {
      this.storeOfflineData('searchHistory', searchEntry);
    }
  }

  // Store analytics data offline
  async storeAnalyticsData(eventData) {
    const analyticsEntry = {
      ...eventData,
      timestamp: Date.now(),
      synced: false
    };

    if (this.isOnline) {
      try {
        await this.syncAnalyticsData(analyticsEntry);
      } catch (error) {
        console.warn('[OfflineSync] Failed to sync analytics online, storing offline');
        this.storeOfflineData('analyticsQueue', analyticsEntry);
      }
    } else {
      this.storeOfflineData('analyticsQueue', analyticsEntry);
    }
  }

  // Handle connection restored
  async handleConnectionRestored() {
    console.log('[OfflineSync] Connection restored - starting sync process');
    
    if (this.syncInProgress) {
      console.log('[OfflineSync] Sync already in progress');
      return;
    }

    this.syncInProgress = true;

    try {
      await Promise.all([
        this.syncAllUserPreferences(),
        this.syncAllSearchHistory(),
        this.syncAllAnalyticsData(),
        this.syncAllComparisonHistory(),
        this.syncAllFavorites()
      ]);

      console.log('[OfflineSync] All data synced successfully');
      
      // Notify UI about successful sync
      window.dispatchEvent(new CustomEvent('offline-sync-complete', {
        detail: { success: true, timestamp: Date.now() }
      }));

    } catch (error) {
      console.error('[OfflineSync] Sync failed:', error);
      
      // Notify UI about sync failure
      window.dispatchEvent(new CustomEvent('offline-sync-error', {
        detail: { error: error.message, timestamp: Date.now() }
      }));
    } finally {
      this.syncInProgress = false;
    }
  }

  // Sync user preferences to Firebase
  async syncUserPreferences(userId, preferences) {
    const userRef = doc(db, 'users', userId);
    await updateDoc(userRef, {
      preferences,
      lastUpdated: serverTimestamp()
    });
  }

  // Sync search history to Firebase
  async syncSearchHistory(userId, searchEntry) {
    const userRef = doc(db, 'users', userId);
    await updateDoc(userRef, {
      searchHistory: arrayUnion(searchEntry),
      lastSearched: serverTimestamp()
    });
  }

  // Sync analytics data to Firebase
  async syncAnalyticsData(analyticsEntry) {
    const analyticsRef = doc(db, 'analytics', `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
    await setDoc(analyticsRef, {
      ...analyticsEntry,
      syncedAt: serverTimestamp()
    });
  }

  // Sync all stored user preferences
  async syncAllUserPreferences() {
    const storedPreferences = this.getOfflineData('userPreferences') || [];
    
    for (const prefData of storedPreferences) {
      try {
        await this.syncUserPreferences(prefData.userId, prefData.preferences);
      } catch (error) {
        console.error('[OfflineSync] Failed to sync user preferences:', error);
      }
    }

    if (storedPreferences.length > 0) {
      this.clearOfflineData('userPreferences');
    }
  }

  // Sync all stored search history
  async syncAllSearchHistory() {
    const storedSearches = this.getOfflineData('searchHistory') || [];
    
    for (const searchData of storedSearches) {
      try {
        await this.syncSearchHistory(searchData.userId, searchData);
      } catch (error) {
        console.error('[OfflineSync] Failed to sync search history:', error);
      }
    }

    if (storedSearches.length > 0) {
      this.clearOfflineData('searchHistory');
    }
  }

  // Sync all stored analytics data
  async syncAllAnalyticsData() {
    const storedAnalytics = this.getOfflineData('analyticsQueue') || [];
    
    for (const analyticsData of storedAnalytics) {
      try {
        await this.syncAnalyticsData(analyticsData);
      } catch (error) {
        console.error('[OfflineSync] Failed to sync analytics data:', error);
      }
    }

    if (storedAnalytics.length > 0) {
      this.clearOfflineData('analyticsQueue');
    }
  }

  // Sync comparison history
  async syncAllComparisonHistory() {
    const storedComparisons = this.getOfflineData('comparisonHistory') || [];
    
    for (const comparisonData of storedComparisons) {
      try {
        const userRef = doc(db, 'users', comparisonData.userId);
        await updateDoc(userRef, {
          comparisonHistory: arrayUnion(comparisonData),
          lastComparison: serverTimestamp()
        });
      } catch (error) {
        console.error('[OfflineSync] Failed to sync comparison history:', error);
      }
    }

    if (storedComparisons.length > 0) {
      this.clearOfflineData('comparisonHistory');
    }
  }

  // Sync favorites
  async syncAllFavorites() {
    const storedFavorites = this.getOfflineData('favorites') || [];
    
    for (const favoriteData of storedFavorites) {
      try {
        const userRef = doc(db, 'users', favoriteData.userId);
        await updateDoc(userRef, {
          favorites: favoriteData.favorites,
          lastFavoriteUpdate: serverTimestamp()
        });
      } catch (error) {
        console.error('[OfflineSync] Failed to sync favorites:', error);
      }
    }

    if (storedFavorites.length > 0) {
      this.clearOfflineData('favorites');
    }
  }

  // Handle background sync from service worker
  async handleBackgroundSync() {
    if (!this.isOnline) return;
    
    console.log('[OfflineSync] Background sync triggered');
    await this.handleConnectionRestored();
  }

  // Get sync status
  getSyncStatus() {
    const pendingData = {
      userPreferences: (this.getOfflineData('userPreferences') || []).length,
      searchHistory: (this.getOfflineData('searchHistory') || []).length,
      analyticsQueue: (this.getOfflineData('analyticsQueue') || []).length,
      comparisonHistory: (this.getOfflineData('comparisonHistory') || []).length,
      favorites: (this.getOfflineData('favorites') || []).length
    };

    const totalPending = Object.values(pendingData).reduce((sum, count) => sum + count, 0);

    return {
      isOnline: this.isOnline,
      syncInProgress: this.syncInProgress,
      pendingData,
      totalPending
    };
  }
}

// Create singleton instance
const offlineSyncManager = new OfflineSyncManager();

export default offlineSyncManager;
