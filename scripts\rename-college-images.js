// Script to rename existing college images to match the expected format
const fs = require('fs');
const path = require('path');

// Mapping from current image names to expected filenames
const imageNameMapping = {
  // Current filename → Expected filename
  'AMC College of Engineering.jpg': 'amcce.jpg',
  'Acharya Institute of Technology.jpg': 'acharya-it.jpg',
  'Alliance university.jpg': 'alliance.jpg',
  'Atria Institute of Technology.jpg': 'ait.jpg',
  'BMS College of Engineering.jpg': 'bmsce.jpg',
  'BMS Institute of Technology.jpg': 'bmsit.jpg',
  'BNM Institute of Technology.jpg': 'bnmit.jpg',
  'Bangalore Institute of Technology.jpg': 'bit.jpg',
  'Bapuji Institute of Engineering and Technology.jpg': 'biet.jpg',
  'Brindavan College of Engineering.jpg': 'bce.jpg',
  'CMR Institute of Technology.jpg': 'cmrit.jpg',
  'CMR University.jpg': 'cmru.jpg',
  'Cambridge Institute of Technology.jpg': 'cit.jpg',
  'Dayananad Sagar Academy of Technology and Management.jpg': 'dsatm.jpg',
  'Dayananada Sagar College of Engineering.jpg': 'dsce.jpg',
  'Dayananda Sagar University.jpg': 'dsu.jpg',
  'Don Bosco Institute of Technology.jpg': 'dbit.jpg',
  'Dr. B R Ambedkar Institute of Technology.jpg': 'dr-br-ait.jpg',
  'East Point College of Engineering.jpg': 'epce.jpg',
  'East West Institute of Technology.jpg': 'ewit.jpg',
  'Impact College of Engineering.JPG': 'ice.jpg',
  'JSS Academy of Technical Education.jpg': 'jssate.jpg',
  'Jain University.JPG': 'jain.jpg',
  'Kammavari Sangha Institute Of Technology (KSIT).jpg': 'knsit.jpg',
  'MS Ramaiah Institute of Technology.jpg': 'msrit.jpg',
  'MS Ramaiah University of Applied Sciences.jpg': 'msruas.jpg',
  'MVJ College of Engineering.JPG': 'mvj.jpg',
  'Nagarjuna College of Engineering and Technology.jpg': 'ncet.jpg',
  'National Institute of Engineering.jpg': 'nie.jpg',
  'New Horizon College of Engineering.jpg': 'nhce.jpg',
  'Nitte Meenakshi Institute of Technology.jpg': 'nmit.jpg',
  'Oxford College of Engineering.jpg': 'oce.jpg',
  'PES University (Electronic City Campus).jpg': 'pesuecc.jpg',
  'PES University (Ring Road Campus).jpg': 'pesurrc.jpg',
  'Presidency University.jpg': 'pu.jpg',
  'R.N. Shetty Institute of Technology.jpg': 'rnsit.jpg',
  'RR Institute of Technology.jpg': 'rrit.jpg',
  'RV university.jpg': 'rvu.jpg',
  'Reva University.jpg': 'reva.jpg',
  'SJ College Of Engineering (SJCE Mysore).jpg': 'sjce.jpg',
  'SJC Institute of Technology.jpg': 'sjcit.jpg',
  'Sambhram Institute of Technology.jpg': 'sambhram.jpg',
  'Sapthagiri NPS university.jpg': 'sce.jpg',
  'Sir M Visvesvaraya Institute of Technology.jpg': 'sir-mvit.jpg',
  'Sri Krishna Institute of Technology.jpg': 'skit.jpg',
  'Sri Venkateswara College of Engineering.jpg': 'svce.jpg',
  'T-John Institute of Technology.jpg': 'tjit.jpg',
  'Vijaya Vittala Institute of Technology.jpg': 'vvit.jpg',
  'Vivekananda Institute of Technology.jpg': 'vit.jpg',
  'rvce.jpg': 'rvce.jpg', // Already correct
  'rvit.jpg': 'rvitm.jpg' // Rename to match RVITM
};

// College ID to expected filename mapping - CORRECTED TO MATCH YOUR ACTUAL COLLEGE.JSON
const expectedImages = {
  1: 'rvce.jpg',
  2: 'rvitm.jpg',
  3: 'rvu.jpg',
  4: 'pesurrc.jpg',
  5: 'bmsce.jpg',
  6: 'msrit.jpg',
  7: 'sir-mvit.jpg',
  8: 'bit.jpg',
  9: 'nmit.jpg',
  10: 'pesuecc.jpg',
  11: 'cmrit.jpg',
  12: 'dsce.jpg',
  13: 'bmsit.jpg',
  14: 'reva.jpg',
  15: 'msruas.jpg',
  16: 'rnsit.jpg',
  17: 'jssate.jpg',
  18: 'nie.jpg',
  19: 'nhce.jpg',
  20: 'cmru.jpg',
  21: 'bnmit.jpg',
  22: 'dr-br-ait.jpg',
  23: 'dsatm.jpg',
  24: 'dsu.jpg',
  25: 'ait.jpg',
  26: 'pu.jpg',
  27: 'ncet.jpg',
  28: 'svce.jpg',
  29: 'sjce.jpg',
  30: 'sjcit.jpg',
  31: 'mvjce.jpg',
  32: 'biet.jpg',
  33: 'ace.jpg',
  34: 'snpu.jpg',
  35: 'ait-atria.jpg',
  36: 'ju.jpg',
  37: 'oce.jpg',
  38: 'skit.jpg',
  39: 'dbit.jpg',
  40: 'sait.jpg',
  41: 'bce.jpg',
  42: 'ice.jpg',
  43: 'amcce.jpg',
  44: 'cit.jpg',
  45: 'tjit.jpg',
  46: 'vkit.jpg',
  47: 'ewit.jpg',
  48: 'epce.jpg',
  49: 'vvit.jpg',
  50: 'rrit.jpg'
};

function renameCollegeImages() {
  const imagesDir = path.join(__dirname, '../public/images/colleges');
  
  if (!fs.existsSync(imagesDir)) {
    console.log('❌ Images directory does not exist!');
    return;
  }

  console.log('🔄 Renaming college images to match expected format...\n');

  let renamedCount = 0;
  let errorCount = 0;

  // Get all files in the directory
  const files = fs.readdirSync(imagesDir);
  
  // Rename files according to mapping
  Object.entries(imageNameMapping).forEach(([currentName, expectedName]) => {
    const currentPath = path.join(imagesDir, currentName);
    const newPath = path.join(imagesDir, expectedName);
    
    if (fs.existsSync(currentPath)) {
      try {
        // Check if target file already exists
        if (fs.existsSync(newPath) && currentPath !== newPath) {
          console.log(`⚠️  Target file already exists: ${expectedName}`);
          return;
        }
        
        fs.renameSync(currentPath, newPath);
        console.log(`✅ Renamed: ${currentName} → ${expectedName}`);
        renamedCount++;
      } catch (error) {
        console.log(`❌ Error renaming ${currentName}: ${error.message}`);
        errorCount++;
      }
    }
  });

  console.log(`\n📊 Summary:`);
  console.log(`✅ Successfully renamed: ${renamedCount} files`);
  console.log(`❌ Errors: ${errorCount} files`);

  // Check which expected images are now available
  console.log('\n🔍 Checking for expected college images...');
  const availableImages = [];
  const missingImages = [];

  Object.entries(expectedImages).forEach(([id, filename]) => {
    const imagePath = path.join(imagesDir, filename);
    if (fs.existsSync(imagePath)) {
      availableImages.push(filename);
    } else {
      missingImages.push(filename);
    }
  });

  console.log(`\n✅ Available images: ${availableImages.length}/50`);
  console.log(`❌ Missing images: ${missingImages.length}/50`);

  if (missingImages.length > 0) {
    console.log('\n📝 Missing images:');
    missingImages.forEach(img => console.log(`  - ${img}`));
  }

  if (availableImages.length > 0) {
    console.log('\n🎉 Ready to update college data! Run:');
    console.log('node scripts/update-college-images.js');
  }
}

if (require.main === module) {
  renameCollegeImages();
}

module.exports = { renameCollegeImages };
