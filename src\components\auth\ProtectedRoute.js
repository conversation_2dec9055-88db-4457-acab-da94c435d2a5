'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../hooks/useAuth';
import { Loader2, Lock } from 'lucide-react';
import LoginModal from './LoginModal';

const ProtectedRoute = ({ children, fallback = null, redirectTo = null }) => {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [showLoginModal, setShowLoginModal] = useState(false);

  useEffect(() => {
    if (!loading && !user) {
      if (redirectTo) {
        router.push(redirectTo);
      } else {
        setShowLoginModal(true);
      }
    }
  }, [user, loading, redirectTo, router]);

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-primary-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Show login modal if not authenticated and no redirect specified
  if (!user && !redirectTo) {
    return (
      <>
        {fallback || (
          <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="text-center max-w-md mx-auto p-8">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Lock className="w-8 h-8 text-primary-600" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Authentication Required
              </h2>
              <p className="text-gray-600 mb-6">
                Please sign in to access this page and enjoy personalized features.
              </p>
              <button
                onClick={() => setShowLoginModal(true)}
                className="btn-primary"
              >
                Sign In to Continue
              </button>
            </div>
          </div>
        )}
        <LoginModal
          isOpen={showLoginModal}
          onClose={() => setShowLoginModal(false)}
        />
      </>
    );
  }

  // Redirect if specified and not authenticated
  if (!user && redirectTo) {
    return null; // Router will handle the redirect
  }

  // Render children if authenticated
  return children;
};

export default ProtectedRoute;
