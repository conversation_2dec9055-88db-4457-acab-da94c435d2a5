'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { Target, TrendingUp, Users, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import AnalyticsCard from './AnalyticsCard';

const AdmissionPredictor = ({ 
  colleges = [], 
  selectedCollege = null,
  onCollegeSelect = () => {} 
}) => {
  const [studentScore, setStudentScore] = useState(85);
  const [category, setCategory] = useState('general');
  const [predictions, setPredictions] = useState([]);
  const [loading, setLoading] = useState(false);

  // Calculate admission predictions
  useEffect(() => {
    if (colleges.length > 0 && studentScore > 0) {
      setLoading(true);
      setTimeout(() => {
        const newPredictions = calculatePredictions();
        setPredictions(newPredictions);
        setLoading(false);
      }, 300);
    }
  }, [colleges, studentScore, category]);

  const calculatePredictions = () => {
    return colleges.slice(0, 12).map(college => {
      const analytics = college.analytics || college;
      const difficulty = analytics.admissionDifficulty;
      
      // Adjust probability based on student score and category
      let adjustedProbability = difficulty.probability;
      
      // Score adjustment
      const scoreAdjustment = (studentScore - 75) * 2; // Base score 75
      adjustedProbability += scoreAdjustment;
      
      // Category adjustment
      const categoryAdjustments = {
        'general': 0,
        'obc': 10,
        'sc': 20,
        'st': 25
      };
      adjustedProbability += categoryAdjustments[category] || 0;
      
      // Clamp between 5 and 95
      adjustedProbability = Math.max(5, Math.min(95, adjustedProbability));
      
      // Determine status
      let status = 'moderate';
      let statusColor = 'yellow';
      let statusIcon = AlertTriangle;
      
      if (adjustedProbability >= 75) {
        status = 'high';
        statusColor = 'green';
        statusIcon = CheckCircle;
      } else if (adjustedProbability >= 50) {
        status = 'moderate';
        statusColor = 'yellow';
        statusIcon = AlertTriangle;
      } else {
        status = 'low';
        statusColor = 'red';
        statusIcon = XCircle;
      }
      
      return {
        college: college.acronym,
        fullName: college.name,
        ranking: college.ranking,
        difficulty: difficulty.difficulty,
        originalProbability: difficulty.probability,
        adjustedProbability: Math.round(adjustedProbability),
        status,
        statusColor,
        statusIcon,
        cutoffTrend: difficulty.cutoffTrend,
        lastYearCutoff: difficulty.cutoffTrend[difficulty.cutoffTrend.length - 1]?.cutoff || 0
      };
    }).sort((a, b) => b.adjustedProbability - a.adjustedProbability);
  };

  // Get difficulty distribution
  const getDifficultyDistribution = () => {
    const distribution = { 'Very Easy': 0, 'Easy': 0, 'Medium': 0, 'Hard': 0, 'Very Hard': 0 };
    
    predictions.forEach(pred => {
      if (pred.adjustedProbability >= 85) distribution['Very Easy']++;
      else if (pred.adjustedProbability >= 70) distribution['Easy']++;
      else if (pred.adjustedProbability >= 50) distribution['Medium']++;
      else if (pred.adjustedProbability >= 30) distribution['Hard']++;
      else distribution['Very Hard']++;
    });
    
    return Object.entries(distribution).map(([difficulty, count]) => ({
      difficulty,
      count,
      percentage: predictions.length > 0 ? Math.round((count / predictions.length) * 100) : 0
    }));
  };

  // Get cutoff trends for selected college
  const getCutoffTrends = () => {
    if (!selectedCollege) return [];
    
    const college = predictions.find(p => p.college === selectedCollege);
    return college?.cutoffTrend || [];
  };

  const difficultyDistribution = getDifficultyDistribution();
  const cutoffTrends = getCutoffTrends();

  // Custom tooltip for charts
  const PredictionTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-900 mb-2">{label}</p>
          <p className="text-sm">
            Admission Probability: <span className="font-medium">{payload[0].value}%</span>
          </p>
        </div>
      );
    }
    return null;
  };

  const CutoffTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-900 mb-2">Year {label}</p>
          <div className="space-y-1 text-sm">
            <p>Cutoff: <span className="font-medium">{data.cutoff}%</span></p>
            <p>Applicants: <span className="font-medium">{data.applicants}</span></p>
            <p>Seats: <span className="font-medium">{data.seats}</span></p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <AnalyticsCard
      title="Admission Difficulty Predictor"
      subtitle="Predict your admission chances based on scores and category"
      info="Get personalized admission probability for different colleges"
      loading={loading && predictions.length === 0}
      className="col-span-full"
    >
      <div className="space-y-6">
        {/* Input Controls */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Expected Score/Percentile
            </label>
            <div className="relative">
              <input
                type="range"
                min="40"
                max="100"
                value={studentScore}
                onChange={(e) => setStudentScore(Number(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>40</span>
                <span className="font-medium text-primary-600">{studentScore}%</span>
                <span>100</span>
              </div>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category
            </label>
            <select
              value={category}
              onChange={(e) => setCategory(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="general">General</option>
              <option value="obc">OBC</option>
              <option value="sc">SC</option>
              <option value="st">ST</option>
            </select>
          </div>
        </div>

        {/* Summary Stats */}
        {predictions.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-green-50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="text-sm font-medium text-green-700">High Chance</span>
              </div>
              <p className="text-2xl font-bold text-green-900">
                {predictions.filter(p => p.adjustedProbability >= 75).length}
              </p>
              <p className="text-xs text-green-600">colleges</p>
            </div>
            
            <div className="bg-yellow-50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
                <span className="text-sm font-medium text-yellow-700">Moderate</span>
              </div>
              <p className="text-2xl font-bold text-yellow-900">
                {predictions.filter(p => p.adjustedProbability >= 50 && p.adjustedProbability < 75).length}
              </p>
              <p className="text-xs text-yellow-600">colleges</p>
            </div>
            
            <div className="bg-red-50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <XCircle className="h-5 w-5 text-red-600" />
                <span className="text-sm font-medium text-red-700">Low Chance</span>
              </div>
              <p className="text-2xl font-bold text-red-900">
                {predictions.filter(p => p.adjustedProbability < 50).length}
              </p>
              <p className="text-xs text-red-600">colleges</p>
            </div>
            
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Target className="h-5 w-5 text-blue-600" />
                <span className="text-sm font-medium text-blue-700">Best Match</span>
              </div>
              <p className="text-lg font-bold text-blue-900">
                {predictions[0]?.college || 'N/A'}
              </p>
              <p className="text-xs text-blue-600">{predictions[0]?.adjustedProbability}% chance</p>
            </div>
          </div>
        )}

        {/* Predictions Chart */}
        {predictions.length > 0 && (
          <div className="h-80">
            <h4 className="font-medium text-gray-900 mb-4">Admission Probability by College</h4>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={predictions}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis 
                  dataKey="college" 
                  stroke="#6b7280"
                  fontSize={12}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis 
                  stroke="#6b7280"
                  fontSize={12}
                  domain={[0, 100]}
                  label={{ value: 'Probability (%)', angle: -90, position: 'insideLeft' }}
                />
                <Tooltip content={<PredictionTooltip />} />
                <Bar 
                  dataKey="adjustedProbability" 
                  radius={[4, 4, 0, 0]}
                  fill={(entry) => {
                    if (entry >= 75) return '#10b981';
                    if (entry >= 50) return '#f59e0b';
                    return '#ef4444';
                  }}
                >
                  {predictions.map((entry, index) => (
                    <Bar 
                      key={`cell-${index}`} 
                      fill={
                        entry.adjustedProbability >= 75 ? '#10b981' :
                        entry.adjustedProbability >= 50 ? '#f59e0b' : '#ef4444'
                      } 
                    />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        )}

        {/* Detailed Predictions */}
        {predictions.length > 0 && (
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Detailed Predictions</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {predictions.slice(0, 9).map((prediction, index) => {
                const StatusIcon = prediction.statusIcon;
                return (
                  <div key={prediction.college} className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h5 className="font-semibold text-gray-900">{prediction.college}</h5>
                      <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
                        prediction.statusColor === 'green' ? 'bg-green-100 text-green-800' :
                        prediction.statusColor === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        <StatusIcon className="h-3 w-3" />
                        <span>{prediction.adjustedProbability}%</span>
                      </div>
                    </div>
                    
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Ranking:</span>
                        <span className="font-medium">#{prediction.ranking}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Difficulty:</span>
                        <span className="font-medium">{prediction.difficulty}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Last Cutoff:</span>
                        <span className="font-medium">{prediction.lastYearCutoff}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Your Score:</span>
                        <span className={`font-medium ${
                          studentScore >= prediction.lastYearCutoff ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {studentScore}%
                        </span>
                      </div>
                    </div>
                    
                    {/* Progress bar */}
                    <div className="mt-3">
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${
                            prediction.statusColor === 'green' ? 'bg-green-500' :
                            prediction.statusColor === 'yellow' ? 'bg-yellow-500' :
                            'bg-red-500'
                          }`}
                          style={{ width: `${prediction.adjustedProbability}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Cutoff Trends for Selected College */}
        {selectedCollege && cutoffTrends.length > 0 && (
          <div className="border-t pt-6">
            <h4 className="font-medium text-gray-900 mb-4">
              Cutoff Trends - {predictions.find(p => p.college === selectedCollege)?.fullName}
            </h4>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={cutoffTrends}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis dataKey="year" stroke="#6b7280" fontSize={12} />
                  <YAxis stroke="#6b7280" fontSize={12} />
                  <Tooltip content={<CutoffTooltip />} />
                  <Line
                    type="monotone"
                    dataKey="cutoff"
                    stroke="#3b82f6"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}

        {/* College Selection */}
        {predictions.length > 0 && (
          <div className="border-t pt-4">
            <h4 className="font-medium text-gray-900 mb-3">View Cutoff Trends:</h4>
            <div className="flex flex-wrap gap-2">
              {predictions.slice(0, 8).map(prediction => (
                <button
                  key={prediction.college}
                  onClick={() => onCollegeSelect(prediction.college)}
                  className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    selectedCollege === prediction.college
                      ? 'bg-primary-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {prediction.college}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </AnalyticsCard>
  );
};

export default AdmissionPredictor;
