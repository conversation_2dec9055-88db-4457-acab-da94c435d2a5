// Firebase configuration and initialization
import {
  mockAuth,
  mockFirestore,
  mockStorage,
  mockAnalytics,
  serverTimestamp as mockServerTimestamp,
  arrayUnion as mockArrayUnion
} from './mockFirebase';

// Firebase configuration
const firebaseConfig = {
  // Replace with your Firebase config
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || "demo-key",
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || "demo-project.firebaseapp.com",
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || "demo-project",
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || "demo-project.appspot.com",
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || "123456789",
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || "1:123456789:web:abcdef",
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID || "G-ABCDEF"
};

// Demo mode - using mock services for development

// For demo mode, always use mock services to avoid build issues
export const auth = mockAuth;
export const db = mockFirestore;
export const storage = mockStorage;
export const analytics = mockAnalytics;

// Export utility functions
export const serverTimestamp = mockServerTimestamp;
export const arrayUnion = mockArrayUnion;

export default { name: 'demo-app' };
