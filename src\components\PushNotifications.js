'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Off, Setting<PERSON>, Check, X, Clock, AlertCircle } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import pushNotificationManager, { NOTIFICATION_TYPES, DEFAULT_PREFERENCES } from '../lib/pushNotifications';
import { sendMockNotification } from '../lib/mockFirebase';

const PushNotifications = () => {
  const { user } = useAuth();
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [preferences, setPreferences] = useState(DEFAULT_PREFERENCES);
  const [showSettings, setShowSettings] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [permissionStatus, setPermissionStatus] = useState('default');
  const [notifications, setNotifications] = useState([]);

  useEffect(() => {
    if (user) {
      checkSubscriptionStatus();
      loadUserPreferences();
      loadNotificationHistory();
    }
    
    setPermissionStatus(pushNotificationManager.getPermissionStatus());
  }, [user]);

  const checkSubscriptionStatus = async () => {
    if (!user) return;
    
    try {
      const subscribed = await pushNotificationManager.isUserSubscribed(user.uid);
      setIsSubscribed(subscribed);
    } catch (error) {
      console.error('Error checking subscription status:', error);
    }
  };

  const loadUserPreferences = async () => {
    if (!user) return;
    
    try {
      const userPrefs = await pushNotificationManager.getUserPreferences(user.uid);
      setPreferences(userPrefs);
    } catch (error) {
      console.error('Error loading preferences:', error);
    }
  };

  const loadNotificationHistory = async () => {
    // This would load from Firestore in a real implementation
    // For now, we'll use mock data
    setNotifications([
      {
        id: '1',
        title: 'Application Deadline Reminder',
        body: 'RVCE application deadline is in 3 days',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        type: NOTIFICATION_TYPES.APPLICATION_DEADLINE,
        read: false
      },
      {
        id: '2',
        title: 'New Placement Update',
        body: 'MSRIT reports 95% placement rate for 2024',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
        type: NOTIFICATION_TYPES.PLACEMENT_UPDATE,
        read: true
      }
    ]);
  };

  const handleSubscribe = async () => {
    if (!user) return;
    
    setIsLoading(true);
    try {
      await pushNotificationManager.subscribeUser(user.uid);
      setIsSubscribed(true);
      setPermissionStatus('granted');
      
      // Show success message
      showNotificationMessage('Successfully subscribed to notifications!', 'success');
    } catch (error) {
      console.error('Error subscribing to notifications:', error);
      showNotificationMessage('Failed to enable notifications. Please try again.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUnsubscribe = async () => {
    if (!user) return;
    
    setIsLoading(true);
    try {
      await pushNotificationManager.unsubscribeUser(user.uid);
      setIsSubscribed(false);
      
      showNotificationMessage('Successfully unsubscribed from notifications.', 'success');
    } catch (error) {
      console.error('Error unsubscribing from notifications:', error);
      showNotificationMessage('Failed to disable notifications. Please try again.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePreferenceChange = async (type, field, value) => {
    const updatedPreferences = {
      ...preferences,
      [type]: {
        ...preferences[type],
        [field]: value
      }
    };
    
    setPreferences(updatedPreferences);
    
    if (user) {
      try {
        await pushNotificationManager.saveUserPreferences(user.uid, updatedPreferences);
      } catch (error) {
        console.error('Error saving preferences:', error);
      }
    }
  };

  const showNotificationMessage = (message, type) => {
    // Create temporary notification
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
      type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 3000);
  };

  // Test notification function (for demo mode)
  const sendTestNotification = () => {
    if (process.env.NEXT_PUBLIC_DEMO_MODE === 'true') {
      sendMockNotification(
        'Test Notification',
        'This is a test notification from BEC Compare!',
        { actionUrl: '/colleges' }
      );
      showNotificationMessage('Test notification sent!', 'success');
    }
  };

  const getNotificationTypeLabel = (type) => {
    const labels = {
      [NOTIFICATION_TYPES.APPLICATION_DEADLINE]: 'Application Deadlines',
      [NOTIFICATION_TYPES.STATUS_UPDATE]: 'Status Updates',
      [NOTIFICATION_TYPES.PERSONALIZED_REMINDER]: 'Personal Reminders',
      [NOTIFICATION_TYPES.COLLEGE_EVENT]: 'College Events',
      [NOTIFICATION_TYPES.PLACEMENT_UPDATE]: 'Placement Updates',
      [NOTIFICATION_TYPES.ADMISSION_ALERT]: 'Admission Alerts'
    };
    return labels[type] || type;
  };

  const formatTimestamp = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
    if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    return 'Just now';
  };

  if (!user) return null;

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            {isSubscribed ? (
              <Bell className="w-6 h-6 text-blue-600" />
            ) : (
              <BellOff className="w-6 h-6 text-gray-400" />
            )}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Push Notifications</h3>
            <p className="text-sm text-gray-600">
              {isSubscribed ? 'Notifications enabled' : 'Get notified about important updates'}
            </p>
          </div>
        </div>
        
        <button
          onClick={() => setShowSettings(!showSettings)}
          className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
        >
          <Settings className="w-5 h-5" />
        </button>
      </div>

      {/* Permission Status */}
      {permissionStatus === 'denied' && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 text-red-600" />
            <p className="text-sm text-red-800">
              Notifications are blocked. Please enable them in your browser settings.
            </p>
          </div>
        </div>
      )}

      {permissionStatus === 'unsupported' && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 text-yellow-600" />
            <p className="text-sm text-yellow-800">
              Push notifications are not supported in your browser.
            </p>
          </div>
        </div>
      )}

      {/* Main Controls */}
      <div className="space-y-4">
        {!isSubscribed ? (
          <button
            onClick={handleSubscribe}
            disabled={isLoading || permissionStatus === 'denied' || permissionStatus === 'unsupported'}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>Enabling...</span>
              </>
            ) : (
              <>
                <Bell className="w-5 h-5" />
                <span>Enable Notifications</span>
              </>
            )}
          </button>
        ) : (
          <button
            onClick={handleUnsubscribe}
            disabled={isLoading}
            className="w-full bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>Disabling...</span>
              </>
            ) : (
              <>
                <BellOff className="w-5 h-5" />
                <span>Disable Notifications</span>
              </>
            )}
          </button>
        )}

        {/* Demo Test Button */}
        {process.env.NEXT_PUBLIC_DEMO_MODE === 'true' && isSubscribed && (
          <button
            onClick={sendTestNotification}
            className="w-full bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
          >
            <Bell className="w-5 h-5" />
            <span>Send Test Notification</span>
          </button>
        )}

        {/* Settings Panel */}
        {showSettings && isSubscribed && (
          <div className="border-t pt-4">
            <h4 className="font-semibold text-gray-900 mb-4">Notification Preferences</h4>
            
            <div className="space-y-4">
              {Object.entries(preferences).map(([type, prefs]) => (
                <div key={type} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <p className="font-medium text-gray-900 text-sm">
                      {getNotificationTypeLabel(type)}
                    </p>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <select
                      value={prefs.frequency}
                      onChange={(e) => handlePreferenceChange(type, 'frequency', e.target.value)}
                      className="text-xs border border-gray-300 rounded px-2 py-1"
                      disabled={!prefs.enabled}
                    >
                      <option value="immediate">Immediate</option>
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly</option>
                    </select>
                    
                    <button
                      onClick={() => handlePreferenceChange(type, 'enabled', !prefs.enabled)}
                      className={`w-10 h-6 rounded-full transition-colors duration-200 ${
                        prefs.enabled ? 'bg-blue-600' : 'bg-gray-300'
                      }`}
                    >
                      <div className={`w-4 h-4 bg-white rounded-full transition-transform duration-200 ${
                        prefs.enabled ? 'translate-x-5' : 'translate-x-1'
                      }`} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Recent Notifications */}
        {notifications.length > 0 && (
          <div className="border-t pt-4">
            <h4 className="font-semibold text-gray-900 mb-4">Recent Notifications</h4>
            
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-3 rounded-lg border ${
                    notification.read ? 'bg-gray-50 border-gray-200' : 'bg-blue-50 border-blue-200'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <p className="font-medium text-gray-900 text-sm">
                        {notification.title}
                      </p>
                      <p className="text-gray-600 text-sm mt-1">
                        {notification.body}
                      </p>
                      <div className="flex items-center space-x-2 mt-2">
                        <Clock className="w-3 h-3 text-gray-400" />
                        <span className="text-xs text-gray-500">
                          {formatTimestamp(notification.timestamp)}
                        </span>
                      </div>
                    </div>
                    
                    {!notification.read && (
                      <div className="w-2 h-2 bg-blue-600 rounded-full ml-2 mt-1"></div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PushNotifications;
