'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import CollegeCard from '../CollegeCard';

const LazyCollegeGrid = ({ colleges, itemsPerPage = 12 }) => {
  const [visibleColleges, setVisibleColleges] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const observerRef = useRef();
  const loadingRef = useRef();

  // Load initial colleges
  useEffect(() => {
    const initialColleges = colleges.slice(0, itemsPerPage);
    setVisibleColleges(initialColleges);
    setCurrentPage(1);
  }, [colleges, itemsPerPage]);

  // Load more colleges
  const loadMoreColleges = useCallback(() => {
    if (isLoading) return;
    
    setIsLoading(true);
    
    // Simulate loading delay for better UX
    setTimeout(() => {
      const startIndex = currentPage * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      const newColleges = colleges.slice(startIndex, endIndex);
      
      if (newColleges.length > 0) {
        setVisibleColleges(prev => [...prev, ...newColleges]);
        setCurrentPage(prev => prev + 1);
      }
      
      setIsLoading(false);
    }, 300);
  }, [colleges, currentPage, itemsPerPage, isLoading]);

  // Intersection Observer for infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !isLoading) {
          const hasMoreColleges = visibleColleges.length < colleges.length;
          if (hasMoreColleges) {
            loadMoreColleges();
          }
        }
      },
      { threshold: 0.1 }
    );

    observerRef.current = observer;

    if (loadingRef.current) {
      observer.observe(loadingRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [loadMoreColleges, isLoading, visibleColleges.length, colleges.length]);

  const hasMoreColleges = visibleColleges.length < colleges.length;

  return (
    <div>
      {/* College Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {visibleColleges.map((college, index) => (
          <CollegeCard 
            key={college.id} 
            college={college}
            // Prioritize loading first 6 images
            priority={index < 6}
          />
        ))}
      </div>

      {/* Loading Indicator */}
      {hasMoreColleges && (
        <div 
          ref={loadingRef}
          className="flex justify-center items-center py-8"
        >
          {isLoading ? (
            <div className="flex items-center space-x-2 text-primary-600">
              <svg className="w-6 h-6 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span className="text-lg font-medium">Loading more colleges...</span>
            </div>
          ) : (
            <button
              onClick={loadMoreColleges}
              className="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200"
            >
              Load More Colleges
            </button>
          )}
        </div>
      )}

      {/* End Message */}
      {!hasMoreColleges && visibleColleges.length > 0 && (
        <div className="text-center py-8">
          <p className="text-gray-600 text-lg">
            You've viewed all {colleges.length} colleges! 🎓
          </p>
          <p className="text-gray-500 text-sm mt-2">
            Use the filters above to refine your search.
          </p>
        </div>
      )}
    </div>
  );
};

export default LazyCollegeGrid;
