'use client';

import { useState, useEffect } from 'react';
import { Heart, Share2, ArrowLeft, ArrowRight, MoreVertical } from 'lucide-react';
import SwipeGestures from './SwipeGestures';
import { useRouter } from 'next/navigation';

const MobileCollegeComparison = ({ colleges = [], initialIndex = 0 }) => {
  const router = useRouter();
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [favorites, setFavorites] = useState(new Set());
  const [showActions, setShowActions] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const currentCollege = colleges[currentIndex];
  const nextCollege = colleges[currentIndex + 1];
  const prevCollege = colleges[currentIndex - 1];

  useEffect(() => {
    // Load favorites from localStorage
    const savedFavorites = localStorage.getItem('college-favorites');
    if (savedFavorites) {
      setFavorites(new Set(JSON.parse(savedFavorites)));
    }
  }, []);

  // Handle swipe left (next college)
  const handleSwipeLeft = () => {
    if (currentIndex < colleges.length - 1) {
      navigateToCollege(currentIndex + 1);
    }
  };

  // Handle swipe right (previous college)
  const handleSwipeRight = () => {
    if (currentIndex > 0) {
      navigateToCollege(currentIndex - 1);
    }
  };

  // Navigate to specific college with animation
  const navigateToCollege = (newIndex) => {
    if (isTransitioning || newIndex < 0 || newIndex >= colleges.length) return;
    
    setIsTransitioning(true);
    setCurrentIndex(newIndex);
    
    // Reset transition state after animation
    setTimeout(() => {
      setIsTransitioning(false);
    }, 300);
  };

  // Toggle favorite
  const toggleFavorite = (collegeId) => {
    const newFavorites = new Set(favorites);
    if (newFavorites.has(collegeId)) {
      newFavorites.delete(collegeId);
    } else {
      newFavorites.add(collegeId);
    }
    
    setFavorites(newFavorites);
    localStorage.setItem('college-favorites', JSON.stringify([...newFavorites]));
    
    // Haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(25);
    }
  };

  // Share college
  const shareCollege = async (college) => {
    const shareData = {
      title: college.name,
      text: `Check out ${college.name} - Rank #${college.rank}`,
      url: `${window.location.origin}/colleges/${college.id}`
    };

    try {
      if (navigator.share) {
        await navigator.share(shareData);
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(shareData.url);
        showToast('Link copied to clipboard!');
      }
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  // Show toast message
  const showToast = (message) => {
    const toast = document.createElement('div');
    toast.className = 'fixed bottom-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-2 rounded-lg z-50';
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => {
      if (toast.parentElement) {
        toast.remove();
      }
    }, 2000);
  };

  // Get placement rate color
  const getPlacementColor = (rate) => {
    if (rate >= 90) return 'text-green-600 bg-green-100';
    if (rate >= 75) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  if (!currentCollege) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">No colleges to display</p>
      </div>
    );
  }

  return (
    <div className="relative bg-white rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white">
        <div className="flex items-center space-x-3">
          <span className="text-sm font-medium">
            {currentIndex + 1} of {colleges.length}
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowActions(!showActions)}
            className="p-2 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors duration-200"
          >
            <MoreVertical className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Actions Menu */}
      {showActions && (
        <div className="absolute top-16 right-4 bg-white rounded-lg shadow-xl border border-gray-200 z-20 min-w-[160px]">
          <button
            onClick={() => {
              toggleFavorite(currentCollege.id);
              setShowActions(false);
            }}
            className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-200"
          >
            <Heart 
              className={`w-5 h-5 ${
                favorites.has(currentCollege.id) 
                  ? 'text-red-600 fill-current' 
                  : 'text-gray-400'
              }`} 
            />
            <span className="text-sm">
              {favorites.has(currentCollege.id) ? 'Remove from Favorites' : 'Add to Favorites'}
            </span>
          </button>
          
          <button
            onClick={() => {
              shareCollege(currentCollege);
              setShowActions(false);
            }}
            className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-200"
          >
            <Share2 className="w-5 h-5 text-gray-400" />
            <span className="text-sm">Share College</span>
          </button>
          
          <button
            onClick={() => {
              router.push(`/colleges/${currentCollege.id}`);
              setShowActions(false);
            }}
            className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-200 border-t border-gray-100"
          >
            <span className="text-sm font-medium text-blue-600">View Details</span>
          </button>
        </div>
      )}

      {/* Swipeable Content */}
      <SwipeGestures
        onSwipeLeft={handleSwipeLeft}
        onSwipeRight={handleSwipeRight}
        className="relative"
        threshold={60}
      >
        <div className={`transition-all duration-300 ${isTransitioning ? 'opacity-50' : 'opacity-100'}`}>
          {/* College Image */}
          <div className="relative h-48 bg-gradient-to-br from-blue-100 to-blue-200">
            <img
              src={currentCollege.image || '/images/colleges/default.jpg'}
              alt={currentCollege.name}
              className="w-full h-full object-cover"
              onError={(e) => {
                e.target.src = '/images/colleges/default.jpg';
              }}
            />
            
            {/* Rank Badge */}
            <div className="absolute top-4 left-4 bg-white rounded-full px-3 py-1 shadow-lg">
              <span className="text-sm font-bold text-blue-600">
                Rank #{currentCollege.rank}
              </span>
            </div>
            
            {/* Favorite Button */}
            <button
              onClick={() => toggleFavorite(currentCollege.id)}
              className="absolute top-4 right-4 bg-white rounded-full p-2 shadow-lg"
            >
              <Heart 
                className={`w-5 h-5 ${
                  favorites.has(currentCollege.id) 
                    ? 'text-red-600 fill-current' 
                    : 'text-gray-400'
                }`} 
              />
            </button>
          </div>

          {/* College Info */}
          <div className="p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-2">
              {currentCollege.name}
            </h2>
            
            <p className="text-gray-600 mb-4">
              {currentCollege.location}
            </p>

            {/* Key Metrics */}
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-sm text-gray-600">NIRF Ranking</p>
                <p className="text-2xl font-bold text-gray-900">
                  {currentCollege.nirfRank || 'N/A'}
                </p>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-sm text-gray-600">Placement Rate</p>
                <p className={`text-2xl font-bold ${getPlacementColor(currentCollege.placementRate).split(' ')[0]}`}>
                  {currentCollege.placementRate}%
                </p>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Average Package</span>
                <span className="font-semibold">₹{currentCollege.averagePackage} LPA</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Highest Package</span>
                <span className="font-semibold">₹{currentCollege.highestPackage} LPA</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Total Fees</span>
                <span className="font-semibold">₹{currentCollege.totalFees} L</span>
              </div>
            </div>
          </div>
        </div>
      </SwipeGestures>

      {/* Navigation Indicators */}
      <div className="flex items-center justify-center space-x-2 p-4 bg-gray-50">
        {colleges.map((_, index) => (
          <button
            key={index}
            onClick={() => navigateToCollege(index)}
            className={`w-2 h-2 rounded-full transition-all duration-200 ${
              index === currentIndex 
                ? 'bg-blue-600 w-6' 
                : 'bg-gray-300 hover:bg-gray-400'
            }`}
          />
        ))}
      </div>

      {/* Navigation Arrows */}
      <div className="absolute top-1/2 left-4 transform -translate-y-1/2">
        {prevCollege && (
          <button
            onClick={() => navigateToCollege(currentIndex - 1)}
            className="bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-2 shadow-lg transition-all duration-200"
          >
            <ArrowLeft className="w-5 h-5 text-gray-600" />
          </button>
        )}
      </div>
      
      <div className="absolute top-1/2 right-4 transform -translate-y-1/2">
        {nextCollege && (
          <button
            onClick={() => navigateToCollege(currentIndex + 1)}
            className="bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-2 shadow-lg transition-all duration-200"
          >
            <ArrowRight className="w-5 h-5 text-gray-600" />
          </button>
        )}
      </div>

      {/* Swipe Hint */}
      <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 text-center">
        <p className="text-xs text-gray-500 bg-white bg-opacity-90 px-3 py-1 rounded-full">
          Swipe left/right to compare
        </p>
      </div>
    </div>
  );
};

export default MobileCollegeComparison;
