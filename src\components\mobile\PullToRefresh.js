'use client';

import { useState, useRef, useEffect } from 'react';
import { RefreshCw, ArrowDown } from 'lucide-react';

const PullToRefresh = ({ 
  children, 
  onRefresh, 
  threshold = 80,
  maxPull = 120,
  disabled = false,
  className = '',
  refreshingText = 'Refreshing...',
  pullText = 'Pull to refresh',
  releaseText = 'Release to refresh'
}) => {
  const [pullDistance, setPullDistance] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [canRefresh, setCanRefresh] = useState(false);
  const [startY, setStartY] = useState(0);
  const [isPulling, setIsPulling] = useState(false);
  
  const containerRef = useRef(null);
  const refreshIndicatorRef = useRef(null);

  // Handle touch start
  const handleTouchStart = (e) => {
    if (disabled || isRefreshing) return;
    
    // Only allow pull-to-refresh at the top of the page
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    if (scrollTop > 0) return;
    
    const touch = e.touches[0];
    setStartY(touch.clientY);
    setIsPulling(false);
  };

  // Handle touch move
  const handleTouchMove = (e) => {
    if (disabled || isRefreshing || startY === 0) return;
    
    const touch = e.touches[0];
    const currentY = touch.clientY;
    const deltaY = currentY - startY;
    
    // Only handle downward pulls
    if (deltaY <= 0) {
      setPullDistance(0);
      setIsPulling(false);
      setCanRefresh(false);
      return;
    }
    
    // Check if we're at the top of the page
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    if (scrollTop > 0) return;
    
    // Prevent default scrolling when pulling
    e.preventDefault();
    
    setIsPulling(true);
    
    // Calculate pull distance with resistance
    const resistance = 0.5;
    const adjustedDelta = deltaY * resistance;
    const clampedDistance = Math.min(adjustedDelta, maxPull);
    
    setPullDistance(clampedDistance);
    setCanRefresh(clampedDistance >= threshold);
  };

  // Handle touch end
  const handleTouchEnd = (e) => {
    if (disabled || isRefreshing || !isPulling) {
      setStartY(0);
      setPullDistance(0);
      setIsPulling(false);
      setCanRefresh(false);
      return;
    }
    
    if (canRefresh && pullDistance >= threshold) {
      triggerRefresh();
    } else {
      // Animate back to original position
      animateToPosition(0);
    }
    
    setStartY(0);
    setIsPulling(false);
  };

  // Trigger refresh
  const triggerRefresh = async () => {
    setIsRefreshing(true);
    setCanRefresh(false);
    
    // Haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
    
    // Animate to refresh position
    animateToPosition(threshold);
    
    try {
      if (onRefresh) {
        await onRefresh();
      }
    } catch (error) {
      console.error('Refresh failed:', error);
    } finally {
      // Animate back to original position
      setTimeout(() => {
        animateToPosition(0, () => {
          setIsRefreshing(false);
        });
      }, 500);
    }
  };

  // Animate to specific position
  const animateToPosition = (targetPosition, callback) => {
    const startPosition = pullDistance;
    const distance = targetPosition - startPosition;
    const duration = 300;
    const startTime = Date.now();
    
    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // Easing function (ease-out)
      const easeOut = 1 - Math.pow(1 - progress, 3);
      const currentPosition = startPosition + (distance * easeOut);
      
      setPullDistance(currentPosition);
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        setPullDistance(targetPosition);
        if (callback) callback();
      }
    };
    
    requestAnimationFrame(animate);
  };

  // Get refresh indicator rotation
  const getIndicatorRotation = () => {
    if (isRefreshing) return 'animate-spin';
    
    const rotation = (pullDistance / threshold) * 180;
    return `rotate-[${Math.min(rotation, 180)}deg]`;
  };

  // Get refresh text
  const getRefreshText = () => {
    if (isRefreshing) return refreshingText;
    if (canRefresh) return releaseText;
    return pullText;
  };

  // Get indicator opacity
  const getIndicatorOpacity = () => {
    if (isRefreshing) return 1;
    return Math.min(pullDistance / threshold, 1);
  };

  // Get indicator scale
  const getIndicatorScale = () => {
    if (isRefreshing) return 1;
    const scale = 0.5 + (pullDistance / threshold) * 0.5;
    return Math.min(scale, 1);
  };

  return (
    <div 
      ref={containerRef}
      className={`relative ${className}`}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      style={{
        transform: `translateY(${pullDistance}px)`,
        transition: isPulling ? 'none' : 'transform 0.3s ease-out'
      }}
    >
      {/* Refresh Indicator */}
      <div
        ref={refreshIndicatorRef}
        className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-full z-10"
        style={{
          opacity: getIndicatorOpacity(),
          transform: `translateX(-50%) translateY(-100%) scale(${getIndicatorScale()})`,
          transition: isPulling ? 'none' : 'all 0.3s ease-out'
        }}
      >
        <div className="bg-white rounded-full shadow-lg border border-gray-200 p-4 flex flex-col items-center space-y-2">
          {/* Icon */}
          <div className="relative">
            {isRefreshing ? (
              <RefreshCw className="w-6 h-6 text-blue-600 animate-spin" />
            ) : (
              <ArrowDown 
                className={`w-6 h-6 text-blue-600 transition-transform duration-200 ${
                  canRefresh ? 'rotate-180' : ''
                }`} 
              />
            )}
          </div>
          
          {/* Text */}
          <span className="text-xs text-gray-600 font-medium whitespace-nowrap">
            {getRefreshText()}
          </span>
          
          {/* Progress Ring */}
          {!isRefreshing && (
            <div className="absolute inset-0 rounded-full">
              <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  fill="none"
                  stroke="#e5e7eb"
                  strokeWidth="2"
                />
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  fill="none"
                  stroke="#2563eb"
                  strokeWidth="2"
                  strokeDasharray={`${2 * Math.PI * 45}`}
                  strokeDashoffset={`${2 * Math.PI * 45 * (1 - Math.min(pullDistance / threshold, 1))}`}
                  className="transition-all duration-100"
                />
              </svg>
            </div>
          )}
        </div>
      </div>
      
      {/* Content */}
      <div className={isRefreshing ? 'pointer-events-none' : ''}>
        {children}
      </div>
      
      {/* Background overlay during refresh */}
      {isRefreshing && (
        <div className="absolute inset-0 bg-gray-50 bg-opacity-50 pointer-events-none" />
      )}
    </div>
  );
};

// Hook for programmatic refresh
export const usePullToRefresh = () => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const refresh = async (refreshFunction) => {
    if (isRefreshing) return;
    
    setIsRefreshing(true);
    try {
      if (refreshFunction) {
        await refreshFunction();
      }
    } catch (error) {
      console.error('Programmatic refresh failed:', error);
    } finally {
      setIsRefreshing(false);
    }
  };
  
  return { isRefreshing, refresh };
};

export default PullToRefresh;
