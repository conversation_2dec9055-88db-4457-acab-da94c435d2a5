export const metadata = {
  title: 'Analytics & Insights | College Comparison Platform',
  description: 'Advanced analytics and insights for engineering colleges in Bangalore. Analyze placement trends, salary distributions, ROI calculations, and career paths.',
  keywords: 'college analytics, placement trends, salary analysis, ROI calculator, admission predictor, career paths, engineering colleges bangalore',
  openGraph: {
    title: 'Advanced Analytics & Insights - College Comparison',
    description: 'Comprehensive data analysis and insights for informed college decisions',
    type: 'website',
  },
};

export default function AnalyticsLayout({ children }) {
  return (
    <>
      {children}
    </>
  );
}
