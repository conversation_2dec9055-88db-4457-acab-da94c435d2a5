'use client';

import { useState, useEffect } from 'react';
import { 
  FileText, 
  Plus, 
  Calendar, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Edit,
  Trash2,
  Filter,
  Search,
  ArrowUpDown
} from 'lucide-react';
import { useAuth } from '../../../hooks/useAuth';
import { 
  getUserApplications, 
  addApplication, 
  updateApplication, 
  deleteApplication 
} from '../../../lib/userService';
import { getCollegeById } from '../../../lib/collegeData';

const ApplicationsPage = () => {
  const { user } = useAuth();
  const [applications, setApplications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingApplication, setEditingApplication] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('deadline');

  const applicationStatuses = [
    { value: 'planning', label: 'Planning to Apply', color: 'bg-gray-100 text-gray-800', icon: Clock },
    { value: 'applied', label: 'Applied', color: 'bg-blue-100 text-blue-800', icon: FileText },
    { value: 'accepted', label: 'Accepted', color: 'bg-green-100 text-green-800', icon: CheckCircle },
    { value: 'rejected', label: 'Rejected', color: 'bg-red-100 text-red-800', icon: XCircle },
    { value: 'waitlisted', label: 'Waitlisted', color: 'bg-yellow-100 text-yellow-800', icon: AlertCircle }
  ];

  useEffect(() => {
    loadApplications();
  }, [user]);

  const loadApplications = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const userApplications = await getUserApplications(user.uid);
      
      // Enrich with college data
      const enrichedApplications = await Promise.all(
        userApplications.map(async (app) => {
          const college = await getCollegeById(app.collegeId);
          return { ...app, college };
        })
      );
      
      setApplications(enrichedApplications);
    } catch (error) {
      console.error('Error loading applications:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddApplication = async (applicationData) => {
    try {
      await addApplication(user.uid, applicationData);
      await loadApplications();
      setShowAddModal(false);
    } catch (error) {
      console.error('Error adding application:', error);
    }
  };

  const handleUpdateApplication = async (applicationId, updates) => {
    try {
      await updateApplication(applicationId, updates);
      await loadApplications();
      setEditingApplication(null);
    } catch (error) {
      console.error('Error updating application:', error);
    }
  };

  const handleDeleteApplication = async (applicationId) => {
    if (window.confirm('Are you sure you want to delete this application?')) {
      try {
        await deleteApplication(applicationId);
        await loadApplications();
      } catch (error) {
        console.error('Error deleting application:', error);
      }
    }
  };

  const getStatusInfo = (status) => {
    return applicationStatuses.find(s => s.value === status) || applicationStatuses[0];
  };

  const filteredApplications = applications
    .filter(app => {
      const matchesSearch = !searchQuery || 
        app.college?.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        app.program?.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesStatus = statusFilter === 'all' || app.status === statusFilter;
      
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'deadline':
          return new Date(a.deadline) - new Date(b.deadline);
        case 'status':
          return a.status.localeCompare(b.status);
        case 'college':
          return a.college?.name.localeCompare(b.college?.name) || 0;
        case 'updated':
        default:
          return new Date(b.updatedAt?.toDate()) - new Date(a.updatedAt?.toDate());
      }
    });

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="h-8 bg-gray-200 rounded w-48 animate-pulse"></div>
          <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="bg-white rounded-xl shadow-sm p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-2/3"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
            <FileText className="w-6 h-6 text-blue-600" />
            <span>Application Tracker</span>
          </h1>
          <p className="text-gray-600 mt-1">
            {applications.length} application{applications.length !== 1 ? 's' : ''} tracked
          </p>
        </div>

        <button
          onClick={() => setShowAddModal(true)}
          className="btn-primary flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Add Application</span>
        </button>
      </div>

      {applications.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="w-16 h-16 text-gray-300 mx-auto mb-6" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">No applications yet</h3>
          <p className="text-gray-600 mb-6 max-w-md mx-auto">
            Start tracking your college applications to stay organized and never miss a deadline.
          </p>
          <button
            onClick={() => setShowAddModal(true)}
            className="btn-primary"
          >
            Add Your First Application
          </button>
        </div>
      ) : (
        <>
          {/* Filters */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search applications..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              {/* Status Filter */}
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="all">All Status</option>
                {applicationStatuses.map(status => (
                  <option key={status.value} value={status.value}>
                    {status.label}
                  </option>
                ))}
              </select>

              {/* Sort */}
              <div className="flex items-center space-x-2">
                <ArrowUpDown className="w-4 h-4 text-gray-400" />
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="deadline">Sort by Deadline</option>
                  <option value="status">Sort by Status</option>
                  <option value="college">Sort by College</option>
                  <option value="updated">Sort by Updated</option>
                </select>
              </div>
            </div>
          </div>

          {/* Applications List */}
          {filteredApplications.length === 0 ? (
            <div className="text-center py-8">
              <Search className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">No applications match your search criteria</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredApplications.map((application) => {
                const statusInfo = getStatusInfo(application.status);
                const StatusIcon = statusInfo.icon;
                const isOverdue = new Date(application.deadline) < new Date() && application.status === 'planning';

                return (
                  <div key={application.id} className="bg-white rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-4">
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-1">
                              {application.college?.name || 'Unknown College'}
                            </h3>
                            <p className="text-gray-600">{application.program}</p>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusInfo.color}`}>
                              <StatusIcon className="w-3 h-3 mr-1" />
                              {statusInfo.label}
                            </span>
                            
                            <div className="flex items-center space-x-1">
                              <button
                                onClick={() => setEditingApplication(application)}
                                className="p-1 text-gray-400 hover:text-primary-600 rounded"
                                title="Edit application"
                              >
                                <Edit className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleDeleteApplication(application.id)}
                                className="p-1 text-gray-400 hover:text-red-600 rounded"
                                title="Delete application"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                          <div>
                            <div className="text-sm text-gray-500">Deadline</div>
                            <div className={`font-medium ${isOverdue ? 'text-red-600' : 'text-gray-900'}`}>
                              {new Date(application.deadline).toLocaleDateString()}
                              {isOverdue && <span className="text-xs ml-1">(Overdue)</span>}
                            </div>
                          </div>
                          
                          {application.applicationFee && (
                            <div>
                              <div className="text-sm text-gray-500">Application Fee</div>
                              <div className="font-medium text-gray-900">₹{application.applicationFee}</div>
                            </div>
                          )}
                          
                          <div>
                            <div className="text-sm text-gray-500">Last Updated</div>
                            <div className="font-medium text-gray-900">
                              {new Date(application.updatedAt?.toDate()).toLocaleDateString()}
                            </div>
                          </div>
                        </div>

                        {application.notes && (
                          <div className="bg-gray-50 rounded-lg p-3">
                            <div className="text-sm text-gray-500 mb-1">Notes</div>
                            <p className="text-sm text-gray-700">{application.notes}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </>
      )}

      {/* Add/Edit Application Modal would go here */}
      {/* For brevity, I'll create this as a separate component */}
    </div>
  );
};

export default ApplicationsPage;
