# PWA Icons Setup

This directory should contain the following icon files for the Progressive Web App:

## Required Icon Sizes

### Standard PWA Icons
- `icon-72x72.png` - Small icon for older devices
- `icon-96x96.png` - Standard small icon
- `icon-128x128.png` - Medium icon
- `icon-144x144.png` - Windows tile icon
- `icon-152x152.png` - iPad icon
- `icon-192x192.png` - Standard PWA icon (required)
- `icon-384x384.png` - Large icon
- `icon-512x512.png` - Extra large icon (required)

### Shortcut Icons
- `shortcut-compare.png` - 96x96 icon for Compare shortcut
- `shortcut-browse.png` - 96x96 icon for Browse shortcut
- `shortcut-analytics.png` - 96x96 icon for Analytics shortcut

## Icon Requirements

1. **Format**: PNG format with transparent background
2. **Design**: Should be the BEC Compare logo or a relevant education/college icon
3. **Colors**: Use the app's primary blue theme (#2563eb)
4. **Maskable**: Icons should work well when masked (avoid text near edges)

## Quick Setup

You can create these icons from a single 512x512 source image using online tools like:
- https://realfavicongenerator.net/
- https://www.pwabuilder.com/imageGenerator
- https://favicon.io/favicon-converter/

## Temporary Placeholder

Until you add the actual icons, the PWA will still work but may show default browser icons in some contexts.

## Testing

After adding icons, test the PWA installation on:
- Android Chrome (Add to Home Screen)
- iOS Safari (Add to Home Screen)
- Desktop Chrome (Install App)
- Edge (Install App)
