// Search Analytics Service for tracking user search behavior
import { collection, addDoc, query, where, orderBy, limit, getDocs, serverTimestamp, updateDoc, doc, increment } from 'firebase/firestore';
import { db } from './firebase';
import * as mockAnalytics from './mockAnalytics';

// Check if Firebase is properly configured
const isFirebaseConfigured = () => {
  try {
    return db && process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID !== 'demo-project';
  } catch (error) {
    return false;
  }
};

// Track search queries and user behavior
export const trackSearchQuery = async (searchData) => {
  if (!isFirebaseConfigured()) {
    return mockAnalytics.trackSearchQuery(searchData);
  }

  try {
    const searchQuery = {
      query: searchData.query,
      intent: searchData.intent || 'general',
      entities: searchData.entities || {},
      filters: searchData.filters || {},
      resultsCount: searchData.resultsCount || 0,
      userId: searchData.userId || null,
      sessionId: searchData.sessionId || generateSessionId(),
      timestamp: serverTimestamp(),
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : null,
      source: searchData.source || 'web'
    };

    await addDoc(collection(db, 'searchQueries'), searchQuery);

    // Update search trends
    await updateSearchTrends(searchData.query, searchData.intent);

    return true;
  } catch (error) {
    console.error('Error tracking search query:', error);
    return mockAnalytics.trackSearchQuery(searchData);
  }
};

// Track user interactions with search results
export const trackSearchInteraction = async (interactionData) => {
  if (!isFirebaseConfigured()) {
    return mockAnalytics.trackSearchInteraction(interactionData);
  }

  try {
    const interaction = {
      searchQuery: interactionData.query,
      collegeId: interactionData.collegeId,
      collegeName: interactionData.collegeName,
      action: interactionData.action, // 'view', 'compare', 'favorite', 'contact'
      position: interactionData.position || null, // Position in search results
      userId: interactionData.userId || null,
      sessionId: interactionData.sessionId || generateSessionId(),
      timestamp: serverTimestamp()
    };

    await addDoc(collection(db, 'searchInteractions'), interaction);
    return true;
  } catch (error) {
    console.error('Error tracking search interaction:', error);
    return mockAnalytics.trackSearchInteraction(interactionData);
  }
};

// Update search trends and popular queries
const updateSearchTrends = async (searchQuery, intent) => {
  try {
    const trendsRef = doc(db, 'searchTrends', 'global');
    
    // Extract key terms from query
    const keyTerms = extractKeyTerms(searchQuery);
    
    const updateData = {
      [`queries.${searchQuery.toLowerCase()}`]: increment(1),
      [`intents.${intent}`]: increment(1),
      lastUpdated: serverTimestamp()
    };
    
    // Add key terms
    keyTerms.forEach(term => {
      updateData[`terms.${term}`] = increment(1);
    });
    
    await updateDoc(trendsRef, updateData);
  } catch (error) {
    console.error('Error updating search trends:', error);
  }
};

// Get popular search queries
export const getPopularSearches = async (limitCount = 10) => {
  try {
    const q = query(
      collection(db, 'searchQueries'),
      orderBy('timestamp', 'desc'),
      limit(limitCount * 3) // Get more to filter unique queries
    );
    
    const querySnapshot = await getDocs(q);
    const queries = [];
    const uniqueQueries = new Set();
    
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      if (!uniqueQueries.has(data.query.toLowerCase()) && data.query.length > 3) {
        uniqueQueries.add(data.query.toLowerCase());
        queries.push({
          query: data.query,
          intent: data.intent,
          timestamp: data.timestamp
        });
      }
    });
    
    return queries.slice(0, limitCount);
  } catch (error) {
    console.error('Error getting popular searches:', error);
    return [];
  }
};

// Get search suggestions based on user history and trends
export const getSearchSuggestions = async (partialQuery, userId = null) => {
  if (!isFirebaseConfigured()) {
    return mockAnalytics.getSearchSuggestions(partialQuery, userId);
  }

  try {
    const suggestions = [];

    // Get user's recent searches if logged in
    if (userId) {
      const userSearches = await getUserRecentSearches(userId, 5);
      userSearches.forEach(search => {
        if (search.query.toLowerCase().includes(partialQuery.toLowerCase())) {
          suggestions.push({
            text: search.query,
            type: 'recent',
            intent: search.intent
          });
        }
      });
    }

    // Get popular searches that match partial query
    const popularSearches = await getPopularSearches(20);
    popularSearches.forEach(search => {
      if (search.query.toLowerCase().includes(partialQuery.toLowerCase()) &&
          !suggestions.find(s => s.text === search.query)) {
        suggestions.push({
          text: search.query,
          type: 'popular',
          intent: search.intent
        });
      }
    });

    // Add predefined suggestions
    const predefinedSuggestions = generatePredefinedSuggestions(partialQuery);
    predefinedSuggestions.forEach(suggestion => {
      if (!suggestions.find(s => s.text === suggestion.text)) {
        suggestions.push(suggestion);
      }
    });

    return suggestions.slice(0, 8);
  } catch (error) {
    console.error('Error getting search suggestions:', error);
    return mockAnalytics.getSearchSuggestions(partialQuery, userId);
  }
};

// Get user's recent search history
const getUserRecentSearches = async (userId, limitCount = 10) => {
  try {
    const q = query(
      collection(db, 'searchQueries'),
      where('userId', '==', userId),
      orderBy('timestamp', 'desc'),
      limit(limitCount)
    );
    
    const querySnapshot = await getDocs(q);
    const searches = [];
    
    querySnapshot.forEach((doc) => {
      searches.push(doc.data());
    });
    
    return searches;
  } catch (error) {
    console.error('Error getting user recent searches:', error);
    return [];
  }
};

// Generate predefined search suggestions
const generatePredefinedSuggestions = (partialQuery) => {
  const suggestions = [
    { text: 'engineering colleges with good placements', type: 'template', intent: 'placement' },
    { text: 'top ranked colleges in Bangalore', type: 'template', intent: 'ranking' },
    { text: 'colleges near Electronic City', type: 'template', intent: 'location' },
    { text: 'computer science engineering colleges', type: 'template', intent: 'courses' },
    { text: 'colleges with metro connectivity', type: 'template', intent: 'infrastructure' },
    { text: 'affordable engineering colleges', type: 'template', intent: 'fees' },
    { text: 'RVCE vs MSRIT comparison', type: 'template', intent: 'comparison' },
    { text: 'colleges with highest packages', type: 'template', intent: 'placement' },
    { text: 'autonomous engineering colleges', type: 'template', intent: 'general' },
    { text: 'colleges with research facilities', type: 'template', intent: 'infrastructure' }
  ];
  
  return suggestions.filter(s => 
    s.text.toLowerCase().includes(partialQuery.toLowerCase())
  ).slice(0, 5);
};

// Get search analytics dashboard data
export const getSearchAnalytics = async (timeRange = '7d') => {
  try {
    const endDate = new Date();
    const startDate = new Date();
    
    switch (timeRange) {
      case '1d':
        startDate.setDate(endDate.getDate() - 1);
        break;
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      default:
        startDate.setDate(endDate.getDate() - 7);
    }
    
    const q = query(
      collection(db, 'searchQueries'),
      where('timestamp', '>=', startDate),
      where('timestamp', '<=', endDate),
      orderBy('timestamp', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    const analytics = {
      totalSearches: 0,
      uniqueUsers: new Set(),
      intentDistribution: {},
      topQueries: {},
      avgResultsPerQuery: 0,
      searchTrends: []
    };
    
    let totalResults = 0;
    
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      analytics.totalSearches++;
      
      if (data.userId) {
        analytics.uniqueUsers.add(data.userId);
      }
      
      // Intent distribution
      analytics.intentDistribution[data.intent] = 
        (analytics.intentDistribution[data.intent] || 0) + 1;
      
      // Top queries
      analytics.topQueries[data.query] = 
        (analytics.topQueries[data.query] || 0) + 1;
      
      // Results count
      totalResults += data.resultsCount || 0;
    });
    
    analytics.uniqueUsers = analytics.uniqueUsers.size;
    analytics.avgResultsPerQuery = analytics.totalSearches > 0 ? 
      totalResults / analytics.totalSearches : 0;
    
    return analytics;
  } catch (error) {
    console.error('Error getting search analytics:', error);
    return null;
  }
};

// Helper functions
const extractKeyTerms = (query) => {
  const stopWords = ['the', 'and', 'or', 'in', 'at', 'with', 'for', 'of', 'to', 'a', 'an', 'is', 'are'];
  return query.toLowerCase()
    .split(/\s+/)
    .filter(term => term.length > 2 && !stopWords.includes(term))
    .slice(0, 5); // Limit to 5 key terms
};

const generateSessionId = () => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Track search performance metrics
export const trackSearchPerformance = async (performanceData) => {
  if (!isFirebaseConfigured()) {
    return mockAnalytics.trackSearchPerformance(performanceData);
  }

  try {
    const performance = {
      query: performanceData.query,
      searchTime: performanceData.searchTime, // Time taken to execute search
      resultsCount: performanceData.resultsCount,
      filterCount: performanceData.filterCount,
      timestamp: serverTimestamp(),
      source: performanceData.source || 'web'
    };

    await addDoc(collection(db, 'searchPerformance'), performance);
    return true;
  } catch (error) {
    console.error('Error tracking search performance:', error);
    return mockAnalytics.trackSearchPerformance(performanceData);
  }
};

export default {
  trackSearchQuery,
  trackSearchInteraction,
  getPopularSearches,
  getSearchSuggestions,
  getSearchAnalytics,
  trackSearchPerformance
};
