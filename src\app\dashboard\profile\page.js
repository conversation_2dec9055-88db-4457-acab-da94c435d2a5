'use client';

import { useState, useEffect } from 'react';
import { 
  User, 
  Mail, 
  MapPin, 
  DollarSign, 
  BookOpen, 
  Bell, 
  Save,
  Camera,
  Shield,
  Trash2
} from 'lucide-react';
import { useAuth } from '../../../hooks/useAuth';
import { getUserProfile, updateUserProfile } from '../../../lib/userService';

const ProfilePage = () => {
  const { user, updateUserProfile: updateAuthProfile } = useAuth();
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('personal');
  
  const [formData, setFormData] = useState({
    displayName: '',
    email: '',
    phone: '',
    location: '',
    preferences: {
      budget: { min: 0, max: 1000000 },
      programs: [],
      collegeSize: '',
      location: '',
      notifications: true
    }
  });

  const tabs = [
    { id: 'personal', label: 'Personal Info', icon: User },
    { id: 'preferences', label: 'Preferences', icon: BookOpen },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield }
  ];

  const programOptions = [
    'Computer Science Engineering',
    'Information Technology',
    'Electronics and Communication',
    'Mechanical Engineering',
    'Civil Engineering',
    'Electrical Engineering',
    'Aerospace Engineering',
    'Chemical Engineering',
    'Biotechnology',
    'Industrial Engineering'
  ];

  const collegeSizeOptions = [
    { value: 'small', label: 'Small (< 5,000 students)' },
    { value: 'medium', label: 'Medium (5,000 - 15,000 students)' },
    { value: 'large', label: 'Large (> 15,000 students)' },
    { value: 'any', label: 'Any size' }
  ];

  useEffect(() => {
    loadProfile();
  }, [user]);

  const loadProfile = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const userProfile = await getUserProfile(user.uid);
      
      if (userProfile) {
        setProfile(userProfile);
        setFormData({
          displayName: userProfile.displayName || user.displayName || '',
          email: userProfile.email || user.email || '',
          phone: userProfile.phone || '',
          location: userProfile.location || '',
          preferences: {
            budget: userProfile.preferences?.budget || { min: 0, max: 1000000 },
            programs: userProfile.preferences?.programs || [],
            collegeSize: userProfile.preferences?.collegeSize || '',
            location: userProfile.preferences?.location || '',
            notifications: userProfile.preferences?.notifications !== false
          }
        });
      } else {
        // Initialize with user data
        setFormData({
          displayName: user.displayName || '',
          email: user.email || '',
          phone: '',
          location: '',
          preferences: {
            budget: { min: 0, max: 1000000 },
            programs: [],
            collegeSize: '',
            location: '',
            notifications: true
          }
        });
      }
    } catch (error) {
      console.error('Error loading profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePreferenceChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        [field]: value
      }
    }));
  };

  const handleProgramToggle = (program) => {
    const currentPrograms = formData.preferences.programs;
    const updatedPrograms = currentPrograms.includes(program)
      ? currentPrograms.filter(p => p !== program)
      : [...currentPrograms, program];
    
    handlePreferenceChange('programs', updatedPrograms);
  };

  const handleSave = async () => {
    if (!user) return;

    try {
      setSaving(true);
      
      // Update user profile in Firestore
      await updateUserProfile(user.uid, {
        displayName: formData.displayName,
        phone: formData.phone,
        location: formData.location,
        preferences: formData.preferences
      });

      // Update auth profile if display name changed
      if (formData.displayName !== user.displayName) {
        await updateAuthProfile({ displayName: formData.displayName });
      }

      alert('Profile updated successfully!');
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Error updating profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-gray-200 rounded w-48 animate-pulse"></div>
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i}>
                <div className="h-4 bg-gray-200 rounded w-24 mb-2 animate-pulse"></div>
                <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
            <User className="w-6 h-6 text-primary-600" />
            <span>Profile Settings</span>
          </h1>
          <p className="text-gray-600 mt-1">Manage your account and preferences</p>
        </div>

        <button
          onClick={handleSave}
          disabled={saving}
          className="btn-primary flex items-center space-x-2 disabled:opacity-50"
        >
          <Save className="w-4 h-4" />
          <span>{saving ? 'Saving...' : 'Save Changes'}</span>
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-xl shadow-sm p-4">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                      activeTab === tab.id
                        ? 'bg-primary-50 text-primary-700'
                        : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="text-sm font-medium">{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          <div className="bg-white rounded-xl shadow-sm p-6">
            {activeTab === 'personal' && (
              <div className="space-y-6">
                <h2 className="text-lg font-semibold text-gray-900">Personal Information</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Full Name
                    </label>
                    <input
                      type="text"
                      value={formData.displayName}
                      onChange={(e) => handleInputChange('displayName', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address
                    </label>
                    <input
                      type="email"
                      value={formData.email}
                      disabled
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500"
                    />
                    <p className="text-xs text-gray-500 mt-1">Email cannot be changed</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      placeholder="+91 9876543210"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Location
                    </label>
                    <input
                      type="text"
                      value={formData.location}
                      onChange={(e) => handleInputChange('location', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      placeholder="City, State"
                    />
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'preferences' && (
              <div className="space-y-6">
                <h2 className="text-lg font-semibold text-gray-900">College Preferences</h2>
                
                {/* Budget Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Budget Range (Annual Fees)
                  </label>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <input
                        type="number"
                        value={formData.preferences.budget.min}
                        onChange={(e) => handlePreferenceChange('budget', {
                          ...formData.preferences.budget,
                          min: parseInt(e.target.value) || 0
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        placeholder="Min amount"
                      />
                    </div>
                    <div>
                      <input
                        type="number"
                        value={formData.preferences.budget.max}
                        onChange={(e) => handlePreferenceChange('budget', {
                          ...formData.preferences.budget,
                          max: parseInt(e.target.value) || 1000000
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        placeholder="Max amount"
                      />
                    </div>
                  </div>
                </div>

                {/* Programs of Interest */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Programs of Interest
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {programOptions.map((program) => (
                      <label key={program} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={formData.preferences.programs.includes(program)}
                          onChange={() => handleProgramToggle(program)}
                          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        />
                        <span className="text-sm text-gray-700">{program}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* College Size Preference */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Preferred College Size
                  </label>
                  <select
                    value={formData.preferences.collegeSize}
                    onChange={(e) => handlePreferenceChange('collegeSize', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <option value="">Select preference</option>
                    {collegeSizeOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Location Preference */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Preferred Location
                  </label>
                  <input
                    type="text"
                    value={formData.preferences.location}
                    onChange={(e) => handlePreferenceChange('location', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="e.g., Bangalore, Mumbai, Delhi"
                  />
                </div>
              </div>
            )}

            {activeTab === 'notifications' && (
              <div className="space-y-6">
                <h2 className="text-lg font-semibold text-gray-900">Notification Preferences</h2>
                
                <div className="space-y-4">
                  <label className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={formData.preferences.notifications}
                      onChange={(e) => handlePreferenceChange('notifications', e.target.checked)}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                    <div>
                      <div className="font-medium text-gray-900">Email Notifications</div>
                      <div className="text-sm text-gray-500">
                        Receive updates about new colleges, application deadlines, and recommendations
                      </div>
                    </div>
                  </label>
                </div>
              </div>
            )}

            {activeTab === 'security' && (
              <div className="space-y-6">
                <h2 className="text-lg font-semibold text-gray-900">Security Settings</h2>
                
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-sm text-gray-600">
                    Your account is secured with OAuth authentication. 
                    Password management is handled by your OAuth provider (Google/Facebook).
                  </p>
                </div>

                <div className="border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-medium text-red-600 mb-4">Danger Zone</h3>
                  <button className="flex items-center space-x-2 px-4 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 transition-colors">
                    <Trash2 className="w-4 h-4" />
                    <span>Delete Account</span>
                  </button>
                  <p className="text-sm text-gray-500 mt-2">
                    This action cannot be undone. All your data will be permanently deleted.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
