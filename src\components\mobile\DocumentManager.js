'use client';

import { useState, useEffect } from 'react';
import { 
  FileText, 
  Camera, 
  Upload, 
  Trash2, 
  Eye, 
  Download,
  Plus,
  Search,
  Filter,
  MoreVertical,
  Calendar,
  Tag
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { storage, db, serverTimestamp } from '../../lib/firebase';
import CameraScanner from './CameraScanner';

const DocumentManager = () => {
  const { user } = useAuth();
  const [documents, setDocuments] = useState([]);
  const [filteredDocuments, setFilteredDocuments] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showScanner, setShowScanner] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [isUploading, setIsUploading] = useState(false);
  const [showActions, setShowActions] = useState(null);

  // Document types
  const documentTypes = [
    { value: 'all', label: 'All Documents', icon: FileText },
    { value: 'transcript', label: 'Transcripts', icon: FileText },
    { value: 'marksheet', label: 'Marksheets', icon: FileText },
    { value: 'certificate', label: 'Certificates', icon: FileText },
    { value: 'scorecard', label: 'Scorecards', icon: FileText }
  ];

  useEffect(() => {
    if (user) {
      loadDocuments();
    }
  }, [user]);

  useEffect(() => {
    filterDocuments();
  }, [documents, searchQuery, filterType]);

  // Load user documents
  const loadDocuments = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const documentsCollection = db.collection('userDocuments');
      const querySnapshot = await documentsCollection.where('userId', '==', user.uid).get();
      const userDocuments = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      setDocuments(userDocuments);
    } catch (error) {
      console.error('Error loading documents:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Filter documents based on search and type
  const filterDocuments = () => {
    let filtered = documents;

    // Filter by type
    if (filterType !== 'all') {
      filtered = filtered.filter(doc => doc.type === filterType);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(doc =>
        doc.name.toLowerCase().includes(query) ||
        doc.extractedText?.toLowerCase().includes(query) ||
        doc.type.toLowerCase().includes(query)
      );
    }

    setFilteredDocuments(filtered);
  };

  // Handle camera capture
  const handleCameraCapture = async (captureData) => {
    if (!user) return;

    try {
      setIsUploading(true);

      // Upload image to mock storage
      const storageRef = storage.ref(`documents/${user.uid}/${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`);
      const uploadResult = await storageRef.put(captureData.image.blob);
      const imageUrl = await uploadResult.ref.getDownloadURL();

      // Save document metadata to mock Firestore
      const documentData = {
        userId: user.uid,
        name: `Document_${new Date().toLocaleDateString()}`,
        type: captureData.documentData?.type || 'unknown',
        imageUrl,
        extractedText: captureData.text,
        structuredData: captureData.documentData?.extractedData || {},
        createdAt: serverTimestamp(),
        size: captureData.image.blob.size,
        mimeType: 'image/jpeg'
      };

      const docRef = await db.collection('userDocuments').add(documentData);

      // Add to local state
      setDocuments(prev => [{
        id: docRef.id,
        ...documentData,
        createdAt: new Date()
      }, ...prev]);

      setShowScanner(false);
      showToast('Document saved successfully!');
    } catch (error) {
      console.error('Error saving document:', error);
      showToast('Failed to save document. Please try again.', 'error');
    } finally {
      setIsUploading(false);
    }
  };

  // Handle file upload
  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file || !user) return;

    try {
      setIsUploading(true);

      // Upload file to mock storage
      const fileRef = storage.ref(`documents/${user.uid}/${Date.now()}_${file.name}`);
      const uploadResult = await fileRef.put(file);
      const fileUrl = await uploadResult.ref.getDownloadURL();

      // Save document metadata
      const documentData = {
        userId: user.uid,
        name: file.name,
        type: 'uploaded',
        imageUrl: fileUrl,
        extractedText: '',
        structuredData: {},
        createdAt: serverTimestamp(),
        size: file.size,
        mimeType: file.type
      };

      const docRef = await db.collection('userDocuments').add(documentData);

      setDocuments(prev => [{
        id: docRef.id,
        ...documentData,
        createdAt: new Date()
      }, ...prev]);

      showToast('File uploaded successfully!');
    } catch (error) {
      console.error('Error uploading file:', error);
      showToast('Failed to upload file. Please try again.', 'error');
    } finally {
      setIsUploading(false);
      event.target.value = '';
    }
  };

  // Delete document
  const deleteDocument = async (document) => {
    if (!user) return;

    try {
      // Delete from mock storage
      const imageRef = storage.ref(document.imageUrl);
      await imageRef.delete();

      // Delete from mock Firestore
      await db.collection('userDocuments').doc(document.id).delete();

      // Remove from local state
      setDocuments(prev => prev.filter(doc => doc.id !== document.id));

      showToast('Document deleted successfully!');
    } catch (error) {
      console.error('Error deleting document:', error);
      showToast('Failed to delete document. Please try again.', 'error');
    }
  };

  // Download document
  const downloadDocument = async (document) => {
    try {
      const response = await fetch(document.imageUrl);
      const blob = await response.blob();
      
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = document.name;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      showToast('Document downloaded successfully!');
    } catch (error) {
      console.error('Error downloading document:', error);
      showToast('Failed to download document. Please try again.', 'error');
    }
  };

  // Show toast message
  const showToast = (message, type = 'success') => {
    const toast = document.createElement('div');
    toast.className = `fixed bottom-20 left-1/2 transform -translate-x-1/2 px-4 py-2 rounded-lg z-50 ${
      type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => {
      if (toast.parentElement) {
        toast.remove();
      }
    }, 3000);
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date
  const formatDate = (date) => {
    if (!date) return 'Unknown';
    const d = date.toDate ? date.toDate() : new Date(date);
    return d.toLocaleDateString();
  };

  if (!user) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">Please sign in to manage documents</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-900">Document Manager</h2>
          <div className="flex items-center space-x-2">
            {/* Upload button */}
            <label className="bg-gray-600 hover:bg-gray-700 text-white p-2 rounded-lg cursor-pointer transition-colors duration-200">
              <Upload className="w-5 h-5" />
              <input
                type="file"
                accept="image/*,.pdf"
                onChange={handleFileUpload}
                className="hidden"
                disabled={isUploading}
              />
            </label>
            
            {/* Camera button */}
            <button
              onClick={() => setShowScanner(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-lg transition-colors duration-200"
              disabled={isUploading}
            >
              <Camera className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="flex space-x-3">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search documents..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {documentTypes.map(type => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Documents List */}
      <div className="p-6">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : filteredDocuments.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No documents found</h3>
            <p className="text-gray-600 mb-4">
              {documents.length === 0 
                ? 'Start by scanning or uploading your first document'
                : 'Try adjusting your search or filter criteria'
              }
            </p>
            <button
              onClick={() => setShowScanner(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg flex items-center space-x-2 mx-auto"
            >
              <Camera className="w-5 h-5" />
              <span>Scan Document</span>
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredDocuments.map((document) => (
              <div key={document.id} className="bg-gray-50 rounded-lg p-4 relative">
                {/* Document preview */}
                <div className="aspect-w-3 aspect-h-4 mb-3">
                  <img
                    src={document.imageUrl}
                    alt={document.name}
                    className="w-full h-32 object-cover rounded-lg cursor-pointer"
                    onClick={() => setSelectedDocument(document)}
                  />
                </div>
                
                {/* Document info */}
                <div className="space-y-2">
                  <h4 className="font-medium text-gray-900 truncate">
                    {document.name}
                  </h4>
                  
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Tag className="w-4 h-4" />
                    <span className="capitalize">{document.type}</span>
                  </div>
                  
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Calendar className="w-4 h-4" />
                    <span>{formatDate(document.createdAt)}</span>
                  </div>
                  
                  <div className="text-sm text-gray-500">
                    {formatFileSize(document.size)}
                  </div>
                </div>
                
                {/* Actions menu */}
                <div className="absolute top-2 right-2">
                  <button
                    onClick={() => setShowActions(showActions === document.id ? null : document.id)}
                    className="bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-1 shadow-sm"
                  >
                    <MoreVertical className="w-4 h-4 text-gray-600" />
                  </button>
                  
                  {showActions === document.id && (
                    <div className="absolute top-8 right-0 bg-white rounded-lg shadow-xl border border-gray-200 z-10 min-w-[120px]">
                      <button
                        onClick={() => {
                          setSelectedDocument(document);
                          setShowActions(null);
                        }}
                        className="w-full flex items-center space-x-2 px-3 py-2 text-left hover:bg-gray-50 transition-colors duration-200"
                      >
                        <Eye className="w-4 h-4" />
                        <span className="text-sm">View</span>
                      </button>
                      
                      <button
                        onClick={() => {
                          downloadDocument(document);
                          setShowActions(null);
                        }}
                        className="w-full flex items-center space-x-2 px-3 py-2 text-left hover:bg-gray-50 transition-colors duration-200"
                      >
                        <Download className="w-4 h-4" />
                        <span className="text-sm">Download</span>
                      </button>
                      
                      <button
                        onClick={() => {
                          if (confirm('Are you sure you want to delete this document?')) {
                            deleteDocument(document);
                          }
                          setShowActions(null);
                        }}
                        className="w-full flex items-center space-x-2 px-3 py-2 text-left hover:bg-red-50 text-red-600 transition-colors duration-200"
                      >
                        <Trash2 className="w-4 h-4" />
                        <span className="text-sm">Delete</span>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Camera Scanner Modal */}
      {showScanner && (
        <CameraScanner
          onCapture={handleCameraCapture}
          onClose={() => setShowScanner(false)}
          autoExtractText={true}
        />
      )}

      {/* Document Viewer Modal */}
      {selectedDocument && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
              <h3 className="text-lg font-semibold">{selectedDocument.name}</h3>
              <button
                onClick={() => setSelectedDocument(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            
            <div className="p-4 overflow-y-auto max-h-[calc(90vh-120px)]">
              <img
                src={selectedDocument.imageUrl}
                alt={selectedDocument.name}
                className="w-full rounded-lg mb-4"
              />
              
              {selectedDocument.extractedText && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Extracted Text</h4>
                  <div className="bg-gray-50 rounded-lg p-3 text-sm">
                    {selectedDocument.extractedText}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Loading overlay */}
      {isUploading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-40">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-sm text-gray-600">Uploading document...</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentManager;
