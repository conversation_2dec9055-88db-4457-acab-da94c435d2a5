'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { doc, setDoc, getDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../lib/firebase';

const AuthContext = createContext({});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(false); // Set to false for now
  const [error, setError] = useState(null);

  // Create or update user document in Firestore
  const createUserDocument = async (user, additionalData = {}) => {
    if (!user) return;
    
    const userRef = doc(db, 'users', user.uid);
    const userSnap = await getDoc(userRef);
    
    if (!userSnap.exists()) {
      const { displayName, email, photoURL } = user;
      try {
        await setDoc(userRef, {
          displayName,
          email,
          photoURL,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          preferences: {
            location: '',
            budget: { min: 0, max: 1000000 },
            programs: [],
            collegeSize: '',
            notifications: true
          },
          ...additionalData
        });
      } catch (error) {
        console.error('Error creating user document:', error);
        throw error;
      }
    }
    
    return userRef;
  };

  // Mock sign in with Google (for demo purposes)
  const signInWithGoogle = async () => {
    try {
      setError(null);
      // Create a mock user for demo
      const mockUser = {
        uid: 'demo-google-user',
        displayName: 'Demo Google User',
        email: '<EMAIL>',
        photoURL: 'https://via.placeholder.com/150',
        provider: 'google'
      };

      await createUserDocument(mockUser);
      setUser(mockUser);
      return mockUser;
    } catch (error) {
      setError(error.message);
      throw error;
    }
  };

  // Mock sign in with Facebook (for demo purposes)
  const signInWithFacebook = async () => {
    try {
      setError(null);
      // Create a mock user for demo
      const mockUser = {
        uid: 'demo-facebook-user',
        displayName: 'Demo Facebook User',
        email: '<EMAIL>',
        photoURL: 'https://via.placeholder.com/150',
        provider: 'facebook'
      };

      await createUserDocument(mockUser);
      setUser(mockUser);
      return mockUser;
    } catch (error) {
      setError(error.message);
      throw error;
    }
  };

  // Mock sign up with email and password
  const signUp = async (email, password, displayName) => {
    try {
      setError(null);
      // Create a mock user for demo
      const mockUser = {
        uid: `demo-email-${Date.now()}`,
        displayName,
        email,
        photoURL: null,
        provider: 'email'
      };

      await createUserDocument(mockUser, { displayName });
      setUser(mockUser);
      return mockUser;
    } catch (error) {
      setError(error.message);
      throw error;
    }
  };

  // Mock sign in with email and password
  const signIn = async (email, password) => {
    try {
      setError(null);
      // Create a mock user for demo
      const mockUser = {
        uid: `demo-email-${email.replace('@', '-').replace('.', '-')}`,
        displayName: email.split('@')[0],
        email,
        photoURL: null,
        provider: 'email'
      };

      setUser(mockUser);
      return mockUser;
    } catch (error) {
      setError(error.message);
      throw error;
    }
  };

  // Mock sign out
  const signOut = async () => {
    try {
      setError(null);
      setUser(null);
    } catch (error) {
      setError(error.message);
      throw error;
    }
  };

  // Update user profile
  const updateUserProfile = async (updates) => {
    if (!user) return;
    
    try {
      setError(null);
      const userRef = doc(db, 'users', user.uid);
      await setDoc(userRef, {
        ...updates,
        updatedAt: serverTimestamp()
      }, { merge: true });
    } catch (error) {
      setError(error.message);
      throw error;
    }
  };

  useEffect(() => {
    // For demo purposes, we'll just set loading to false
    // In a real implementation, this would initialize Firebase Auth
    setLoading(false);
  }, []);

  const value = {
    user,
    loading,
    error,
    signInWithGoogle,
    signInWithFacebook,
    signUp,
    signIn,
    signOut,
    updateUserProfile,
    setError
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
