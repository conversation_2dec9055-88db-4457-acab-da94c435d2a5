/**
 * Push Notifications Utility using Firebase Cloud Messaging
 * Handles notification permissions, token management, and message sending
 */

import { db, serverTimestamp } from './firebase';
import { mockMessaging, sendMockNotification } from './mockFirebase';

// Notification types
export const NOTIFICATION_TYPES = {
  APPLICATION_DEADLINE: 'application_deadline',
  STATUS_UPDATE: 'status_update',
  PERSONALIZED_REMINDER: 'personalized_reminder',
  COLLEGE_EVENT: 'college_event',
  PLACEMENT_UPDATE: 'placement_update',
  ADMISSION_ALERT: 'admission_alert'
};

// Default notification preferences
export const DEFAULT_PREFERENCES = {
  [NOTIFICATION_TYPES.APPLICATION_DEADLINE]: { enabled: true, frequency: 'daily' },
  [NOTIFICATION_TYPES.STATUS_UPDATE]: { enabled: true, frequency: 'immediate' },
  [NOTIFICATION_TYPES.PERSONALIZED_REMINDER]: { enabled: true, frequency: 'weekly' },
  [NOTIFICATION_TYPES.COLLEGE_EVENT]: { enabled: false, frequency: 'daily' },
  [NOTIFICATION_TYPES.PLACEMENT_UPDATE]: { enabled: true, frequency: 'weekly' },
  [NOTIFICATION_TYPES.ADMISSION_ALERT]: { enabled: true, frequency: 'immediate' }
};

class PushNotificationManager {
  constructor() {
    this.messaging = null;
    this.currentToken = null;
    this.isSupported = false;
    this.init();
  }

  async init() {
    try {
      // Check if notifications are supported
      this.isSupported = 'Notification' in window && 'serviceWorker' in navigator;

      if (!this.isSupported) {
        console.warn('[Push] Push notifications not supported');
        return;
      }

      // Initialize Firebase Messaging (or mock in demo mode)
      if (process.env.NEXT_PUBLIC_DEMO_MODE === 'true') {
        this.messaging = mockMessaging;
        console.log('[Push] Using mock messaging service');
      } else {
        this.messaging = getMessaging();
      }

      // Set up message listener for foreground messages
      this.setupForegroundMessageListener();

      console.log('[Push] Push notification manager initialized');
    } catch (error) {
      console.error('[Push] Failed to initialize push notifications:', error);
    }
  }

  // Request notification permission
  async requestPermission() {
    if (!this.isSupported) {
      throw new Error('Push notifications not supported');
    }

    try {
      const permission = await Notification.requestPermission();
      
      if (permission === 'granted') {
        console.log('[Push] Notification permission granted');
        await this.getRegistrationToken();
        return true;
      } else {
        console.log('[Push] Notification permission denied');
        return false;
      }
    } catch (error) {
      console.error('[Push] Error requesting notification permission:', error);
      throw error;
    }
  }

  // Get FCM registration token
  async getRegistrationToken() {
    if (!this.messaging) return null;

    try {
      const token = await getToken(this.messaging, {
        vapidKey: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY
      });

      if (token) {
        console.log('[Push] Registration token obtained:', token);
        this.currentToken = token;
        return token;
      } else {
        console.log('[Push] No registration token available');
        return null;
      }
    } catch (error) {
      console.error('[Push] Error getting registration token:', error);
      throw error;
    }
  }

  // Save token to user profile
  async saveTokenToDatabase(userId, token) {
    try {
      const userDoc = db.collection('users').doc(userId);
      await userDoc.update({
        fcmToken: token,
        tokenUpdatedAt: serverTimestamp(),
        notificationsEnabled: true
      });

      console.log('[Push] Token saved to database');
    } catch (error) {
      console.error('[Push] Error saving token to database:', error);
      throw error;
    }
  }

  // Setup foreground message listener
  setupForegroundMessageListener() {
    if (!this.messaging) return;

    onMessage(this.messaging, (payload) => {
      console.log('[Push] Message received in foreground:', payload);
      
      // Show custom notification
      this.showForegroundNotification(payload);
      
      // Track notification received
      this.trackNotificationEvent('received', payload);
    });
  }

  // Show custom notification for foreground messages
  showForegroundNotification(payload) {
    const { notification, data } = payload;
    
    if (!notification) return;

    // Create custom notification UI
    const notificationElement = document.createElement('div');
    notificationElement.className = 'fixed top-4 right-4 bg-white rounded-lg shadow-xl border border-gray-200 p-4 max-w-sm z-50 transform transition-all duration-300 ease-out';
    notificationElement.innerHTML = `
      <div class="flex items-start space-x-3">
        <div class="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
          <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6l-6-6v6z"></path>
          </svg>
        </div>
        <div class="flex-1">
          <h4 class="font-semibold text-gray-900 text-sm">${notification.title}</h4>
          <p class="text-gray-600 text-sm mt-1">${notification.body}</p>
          ${data?.actionUrl ? `<button class="mt-2 text-blue-600 text-sm font-medium hover:text-blue-700">View Details</button>` : ''}
        </div>
        <button class="flex-shrink-0 text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.remove()">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    `;

    // Add click handler for action
    if (data?.actionUrl) {
      const actionButton = notificationElement.querySelector('button:not(:last-child)');
      actionButton?.addEventListener('click', () => {
        window.location.href = data.actionUrl;
        notificationElement.remove();
      });
    }

    // Add to DOM
    document.body.appendChild(notificationElement);

    // Auto remove after 5 seconds
    setTimeout(() => {
      if (notificationElement.parentElement) {
        notificationElement.remove();
      }
    }, 5000);
  }

  // Subscribe user to notifications
  async subscribeUser(userId) {
    try {
      const hasPermission = await this.requestPermission();
      
      if (!hasPermission) {
        throw new Error('Notification permission denied');
      }

      const token = await this.getRegistrationToken();
      
      if (token) {
        await this.saveTokenToDatabase(userId, token);
        await this.saveUserPreferences(userId, DEFAULT_PREFERENCES);
        
        // Track subscription
        this.trackNotificationEvent('subscribed', { userId });
        
        return token;
      } else {
        throw new Error('Failed to get registration token');
      }
    } catch (error) {
      console.error('[Push] Error subscribing user:', error);
      throw error;
    }
  }

  // Unsubscribe user from notifications
  async unsubscribeUser(userId) {
    try {
      const userDoc = db.collection('users').doc(userId);
      await userDoc.update({
        fcmToken: null,
        notificationsEnabled: false,
        tokenUpdatedAt: serverTimestamp()
      });

      // Track unsubscription
      this.trackNotificationEvent('unsubscribed', { userId });

      console.log('[Push] User unsubscribed from notifications');
    } catch (error) {
      console.error('[Push] Error unsubscribing user:', error);
      throw error;
    }
  }

  // Save user notification preferences
  async saveUserPreferences(userId, preferences) {
    try {
      const userDoc = db.collection('users').doc(userId);
      await userDoc.update({
        notificationPreferences: preferences,
        preferencesUpdatedAt: serverTimestamp()
      });

      console.log('[Push] User preferences saved');
    } catch (error) {
      console.error('[Push] Error saving preferences:', error);
      throw error;
    }
  }

  // Get user notification preferences
  async getUserPreferences(userId) {
    try {
      const userDoc = db.collection('users').doc(userId);
      const userData = await userDoc.get();

      if (userData.exists()) {
        return userData.data().notificationPreferences || DEFAULT_PREFERENCES;
      } else {
        return DEFAULT_PREFERENCES;
      }
    } catch (error) {
      console.error('[Push] Error getting user preferences:', error);
      return DEFAULT_PREFERENCES;
    }
  }

  // Schedule notification
  async scheduleNotification(userId, type, title, body, scheduledTime, data = {}) {
    try {
      const notificationCollection = db.collection('scheduledNotifications');
      await notificationCollection.add({
        userId,
        type,
        title,
        body,
        scheduledTime,
        data,
        status: 'scheduled',
        createdAt: serverTimestamp()
      });

      console.log('[Push] Notification scheduled');
    } catch (error) {
      console.error('[Push] Error scheduling notification:', error);
      throw error;
    }
  }

  // Track notification events
  async trackNotificationEvent(event, data) {
    try {
      const analyticsCollection = db.collection('notificationAnalytics');
      await analyticsCollection.add({
        event,
        data,
        timestamp: serverTimestamp(),
        userAgent: navigator.userAgent,
        url: window.location.href
      });
    } catch (error) {
      console.error('[Push] Error tracking notification event:', error);
    }
  }

  // Get notification permission status
  getPermissionStatus() {
    if (!this.isSupported) return 'unsupported';
    return Notification.permission;
  }

  // Check if notifications are enabled for user
  async isUserSubscribed(userId) {
    try {
      const userDoc = db.collection('users').doc(userId);
      const userData = await userDoc.get();

      if (userData.exists()) {
        const data = userData.data();
        return data.notificationsEnabled && data.fcmToken;
      }

      return false;
    } catch (error) {
      console.error('[Push] Error checking subscription status:', error);
      return false;
    }
  }
}

// Create singleton instance
const pushNotificationManager = new PushNotificationManager();

export default pushNotificationManager;
