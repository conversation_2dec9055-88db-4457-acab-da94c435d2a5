// Image preloader utility for better performance

class ImagePreloader {
  constructor() {
    this.cache = new Map();
    this.loadingPromises = new Map();
  }

  // Preload a single image
  preloadImage(src) {
    if (this.cache.has(src)) {
      return Promise.resolve(this.cache.get(src));
    }

    if (this.loadingPromises.has(src)) {
      return this.loadingPromises.get(src);
    }

    const promise = new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        this.cache.set(src, img);
        this.loadingPromises.delete(src);
        resolve(img);
      };

      img.onerror = () => {
        this.loadingPromises.delete(src);
        reject(new Error(`Failed to load image: ${src}`));
      };

      img.src = src;
    });

    this.loadingPromises.set(src, promise);
    return promise;
  }

  // Preload multiple images
  preloadImages(srcArray) {
    return Promise.allSettled(
      srcArray.map(src => this.preloadImage(src))
    );
  }

  // Preload college images based on priority
  preloadCollegeImages(colleges, priority = 'high') {
    let imagesToPreload = [];

    switch (priority) {
      case 'high':
        // Preload first 6 college images (above the fold)
        imagesToPreload = colleges.slice(0, 6).map(college => college.image);
        break;
      case 'medium':
        // Preload first 12 college images
        imagesToPreload = colleges.slice(0, 12).map(college => college.image);
        break;
      case 'low':
        // Preload all college images
        imagesToPreload = colleges.map(college => college.image);
        break;
      default:
        imagesToPreload = colleges.slice(0, 6).map(college => college.image);
    }

    return this.preloadImages(imagesToPreload);
  }

  // Check if image is cached
  isImageCached(src) {
    return this.cache.has(src);
  }

  // Get cached image
  getCachedImage(src) {
    return this.cache.get(src);
  }

  // Clear cache
  clearCache() {
    this.cache.clear();
    this.loadingPromises.clear();
  }

  // Get cache size
  getCacheSize() {
    return this.cache.size;
  }
}

// Create a singleton instance
const imagePreloader = new ImagePreloader();

// Utility functions for easy use
export const preloadImage = (src) => imagePreloader.preloadImage(src);
export const preloadImages = (srcArray) => imagePreloader.preloadImages(srcArray);
export const preloadCollegeImages = (colleges, priority) => imagePreloader.preloadCollegeImages(colleges, priority);
export const isImageCached = (src) => imagePreloader.isImageCached(src);
export const getCachedImage = (src) => imagePreloader.getCachedImage(src);
export const clearImageCache = () => imagePreloader.clearCache();
export const getImageCacheSize = () => imagePreloader.getCacheSize();

export default imagePreloader;
