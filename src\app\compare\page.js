'use client';

import { useState, useEffect } from 'react';
import { Plus, X, ArrowRight, Award, TrendingUp, MapPin, Calendar } from 'lucide-react';
import { getAllColleges, formatCurrency, formatNIRF, getWhatsAppLink } from '../../lib/collegeData';

export default function ComparePage() {
  const [allColleges, setAllColleges] = useState([]);
  const [selectedColleges, setSelectedColleges] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);

  useEffect(() => {
    const loadColleges = async () => {
      try {
        const colleges = await getAllColleges();
        setAllColleges(colleges);
      } catch (error) {
        console.error('Failed to load colleges for comparison:', error);
      }
    };
    loadColleges();
  }, []);

  const filteredColleges = allColleges.filter(college =>
    college.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    college.acronym.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const addCollege = (college) => {
    if (selectedColleges.length < 4 && !selectedColleges.find(c => c.id === college.id)) {
      setSelectedColleges([...selectedColleges, college]);
      setShowAddModal(false);
      setSearchQuery('');
    }
  };

  const removeCollege = (collegeId) => {
    setSelectedColleges(selectedColleges.filter(c => c.id !== collegeId));
  };

  const comparisonFields = [
    { key: 'ranking', label: 'Overall Ranking', format: (value) => `#${value}` },
    { key: 'nirf', label: 'NIRF Ranking', format: formatNIRF },
    { key: 'establishedYear', label: 'Established', format: (value) => value },
    { key: 'placementRate', label: 'Placement Rate', format: (value) => `${value}%` },
    { key: 'highestPackage', label: 'Highest Package', format: formatCurrency },
    { key: 'campusSize', label: 'Campus Size', format: (value) => value },
    { key: 'metroAccess', label: 'Metro Access', format: (value) => value ? 'Yes' : 'No' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="container-max py-8">
          <div className="text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Compare Engineering Colleges
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Compare up to 4 colleges side by side to make an informed decision about your engineering education.
            </p>
          </div>
        </div>
      </div>

      <div className="container-max py-8">
        {/* Add Colleges Section */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Select Colleges to Compare ({selectedColleges.length}/4)
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {/* Selected Colleges */}
            {selectedColleges.map((college) => (
              <div key={college.id} className="card p-4 relative">
                <button
                  onClick={() => removeCollege(college.id)}
                  className="absolute top-2 right-2 text-gray-400 hover:text-red-500 transition-colors duration-200"
                >
                  <X className="h-5 w-5" />
                </button>
                <div className="mb-3">
                  <h3 className="font-semibold text-gray-900 text-sm mb-1">{college.acronym}</h3>
                  <p className="text-xs text-gray-600 line-clamp-2">{college.name}</p>
                </div>
                <div className="flex justify-between items-center text-xs">
                  <span className="bg-primary-100 text-primary-600 px-2 py-1 rounded">
                    #{college.ranking}
                  </span>
                  <span className="text-green-600 font-semibold">
                    {college.placementRate}%
                  </span>
                </div>
              </div>
            ))}

            {/* Add College Button */}
            {selectedColleges.length < 4 && (
              <button
                onClick={() => setShowAddModal(true)}
                className="card p-4 border-2 border-dashed border-gray-300 hover:border-primary-500 hover:bg-primary-50 transition-all duration-200 flex flex-col items-center justify-center text-gray-500 hover:text-primary-600"
              >
                <Plus className="h-8 w-8 mb-2" />
                <span className="font-medium">Add College</span>
              </button>
            )}
          </div>
        </div>

        {/* Comparison Table */}
        {selectedColleges.length >= 2 && (
          <div className="card p-6 mb-8 overflow-x-auto">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Comparison</h2>
            
            <table className="w-full">
              <thead>
                <tr>
                  <th className="text-left py-3 px-4 font-semibold text-gray-900 border-b">
                    Criteria
                  </th>
                  {selectedColleges.map((college) => (
                    <th key={college.id} className="text-center py-3 px-4 font-semibold text-gray-900 border-b min-w-[200px]">
                      <div className="mb-2">
                        <div className="text-sm font-bold">{college.acronym}</div>
                        <div className="text-xs text-gray-600 font-normal">{college.name}</div>
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {comparisonFields.map((field, index) => (
                  <tr key={field.key} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                    <td className="py-4 px-4 font-medium text-gray-900">
                      {field.label}
                    </td>
                    {selectedColleges.map((college) => {
                      const value = college[field.key];
                      const formattedValue = field.format(value);
                      
                      // Highlight best values
                      let isHighlighted = false;
                      if (field.key === 'ranking') {
                        isHighlighted = value === Math.min(...selectedColleges.map(c => c[field.key]));
                      } else if (field.key === 'placementRate' || field.key === 'highestPackage') {
                        isHighlighted = value === Math.max(...selectedColleges.map(c => c[field.key]));
                      } else if (field.key === 'metroAccess') {
                        isHighlighted = value === true;
                      }
                      
                      return (
                        <td key={college.id} className="py-4 px-4 text-center">
                          <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                            isHighlighted 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {formattedValue}
                          </span>
                        </td>
                      );
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Detailed Comparison */}
        {selectedColleges.length >= 2 && (
          <div className="space-y-8">
            {/* Placement Details */}
            <div className="card p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Placement Details</h3>
              <div className="grid gap-6">
                {selectedColleges.map((college) => (
                  <div key={college.id} className="border-l-4 border-primary-500 pl-4">
                    <h4 className="font-semibold text-gray-900 mb-2">{college.acronym}</h4>
                    <p className="text-gray-700 text-sm leading-relaxed">{college.placementDetails}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Infrastructure */}
            <div className="card p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Infrastructure & Facilities</h3>
              <div className="grid gap-6">
                {selectedColleges.map((college) => (
                  <div key={college.id} className="border-l-4 border-secondary-500 pl-4">
                    <h4 className="font-semibold text-gray-900 mb-2">{college.acronym}</h4>
                    <p className="text-gray-700 text-sm leading-relaxed mb-3">{college.infrastructure}</p>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <h5 className="font-medium text-gray-900 mb-1">Laboratory Facilities:</h5>
                      <p className="text-gray-700 text-sm">{college.labs}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Transportation */}
            <div className="card p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Transportation & Connectivity</h3>
              <div className="grid gap-6">
                {selectedColleges.map((college) => (
                  <div key={college.id} className="border-l-4 border-orange-500 pl-4">
                    <h4 className="font-semibold text-gray-900 mb-2">{college.acronym}</h4>
                    <p className="text-gray-700 text-sm leading-relaxed">{college.busAndMetroConvenience}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* CTA Section */}
        {selectedColleges.length > 0 && (
          <div className="bg-primary-600 text-white rounded-xl p-8 text-center">
            <h3 className="text-2xl font-bold mb-4">Need Help Making a Decision?</h3>
            <p className="text-primary-100 mb-6 max-w-2xl mx-auto">
              Get personalized guidance from our education experts to choose the best college for your career goals.
            </p>
            <a
              href={getWhatsAppLink(`I need help comparing these colleges: ${selectedColleges.map(c => c.acronym).join(', ')}`)}
              target="_blank"
              rel="noopener noreferrer"
              className="bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 inline-flex items-center space-x-2"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
              </svg>
              <span>Get Expert Guidance</span>
            </a>
          </div>
        )}

        {/* Empty State */}
        {selectedColleges.length === 0 && (
          <div className="text-center py-12">
            <div className="bg-gray-100 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
              <Award className="h-12 w-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Start Comparing Colleges</h3>
            <p className="text-gray-600 mb-6">
              Add at least 2 colleges to see a detailed comparison.
            </p>
            <button
              onClick={() => setShowAddModal(true)}
              className="btn-primary"
            >
              Add Your First College
            </button>
          </div>
        )}
      </div>

      {/* Add College Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h3 className="text-xl font-bold text-gray-900">Add College to Compare</h3>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
              <div className="mt-4">
                <input
                  type="text"
                  placeholder="Search colleges..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
            </div>
            <div className="p-6 overflow-y-auto max-h-96">
              <div className="space-y-3">
                {filteredColleges
                  .filter(college => !selectedColleges.find(c => c.id === college.id))
                  .slice(0, 10)
                  .map((college) => (
                    <button
                      key={college.id}
                      onClick={() => addCollege(college)}
                      className="w-full text-left p-4 border border-gray-200 rounded-lg hover:border-primary-500 hover:bg-primary-50 transition-all duration-200"
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-semibold text-gray-900">{college.name}</h4>
                          <p className="text-sm text-gray-600">{college.acronym}</p>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-semibold text-primary-600">#{college.ranking}</div>
                          <div className="text-xs text-gray-500">{college.placementRate}% placement</div>
                        </div>
                      </div>
                    </button>
                  ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
