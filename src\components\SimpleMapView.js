'use client';

import { useState, useEffect } from 'react';
import { MapPin, Navigation, Filter, X, Star, TrendingUp, ExternalLink } from 'lucide-react';
import { parseCoordinates, getCurrentLocation, filterCollegesByDistance } from '../lib/geoService';
import { trackSearchInteraction } from '../lib/searchAnalytics';
import { useAuth } from '../hooks/useAuth';

export default function SimpleMapView({ 
  colleges = [], 
  onCollegeSelect,
  selectedCollegeId = null,
  showFilters = true,
  className = ''
}) {
  const [userLocation, setUserLocation] = useState(null);
  const [filteredColleges, setFilteredColleges] = useState(colleges);
  const [filters, setFilters] = useState({
    maxDistance: 50,
    minPlacementRate: 0,
    metroAccess: null,
    showUserLocation: false
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedCollege, setSelectedCollege] = useState(null);
  
  const { user } = useAuth();

  // Update filtered colleges when colleges or filters change
  useEffect(() => {
    applyFilters();
  }, [colleges, filters, userLocation]);

  const applyFilters = () => {
    let filtered = [...colleges];

    // Filter by distance from user location
    if (userLocation && filters.maxDistance < 50) {
      filtered = filterCollegesByDistance(filtered, userLocation, filters.maxDistance);
    }

    // Filter by placement rate
    if (filters.minPlacementRate > 0) {
      filtered = filtered.filter(college => college.placementRate >= filters.minPlacementRate);
    }

    // Filter by metro access
    if (filters.metroAccess !== null) {
      filtered = filtered.filter(college => college.metroAccess === filters.metroAccess);
    }

    setFilteredColleges(filtered);
  };

  const handleGetUserLocation = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const location = await getCurrentLocation();
      setUserLocation(location);
      setFilters(prev => ({ ...prev, showUserLocation: true }));
    } catch (error) {
      setError(error.message);
      console.error('Error getting user location:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCollegeClick = (college) => {
    setSelectedCollege(college);
    if (onCollegeSelect) {
      onCollegeSelect(college);
    }

    // Track interaction
    trackSearchInteraction({
      query: 'map_view',
      collegeId: college.id,
      collegeName: college.name,
      action: 'view',
      userId: user?.uid
    });
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      maxDistance: 50,
      minPlacementRate: 0,
      metroAccess: null,
      showUserLocation: false
    });
    setUserLocation(null);
  };

  return (
    <div className={`relative ${className} bg-gray-50`}>
      {/* Map Filters */}
      {showFilters && (
        <div className="absolute top-4 left-4 z-10 bg-white rounded-lg shadow-lg p-4 max-w-sm">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-gray-900">Filters</h3>
            <button
              onClick={clearFilters}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </button>
          </div>

          <div className="space-y-3">
            {/* User Location */}
            <div>
              <button
                onClick={handleGetUserLocation}
                disabled={isLoading}
                className="w-full flex items-center justify-center px-3 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50 text-sm"
              >
                <Navigation className="h-4 w-4 mr-2" />
                {isLoading ? 'Getting Location...' : 'Find Colleges Near Me'}
              </button>
              {error && (
                <p className="text-red-600 text-xs mt-1">{error}</p>
              )}
            </div>

            {/* Distance Filter */}
            {userLocation && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max Distance: {filters.maxDistance}km
                </label>
                <input
                  type="range"
                  min="5"
                  max="50"
                  value={filters.maxDistance}
                  onChange={(e) => handleFilterChange('maxDistance', parseInt(e.target.value))}
                  className="w-full"
                />
              </div>
            )}

            {/* Placement Rate Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Min Placement Rate
              </label>
              <select
                value={filters.minPlacementRate}
                onChange={(e) => handleFilterChange('minPlacementRate', parseInt(e.target.value))}
                className="w-full border border-gray-300 rounded-md px-2 py-1 text-sm"
              >
                <option value={0}>Any</option>
                <option value={70}>70%+</option>
                <option value={80}>80%+</option>
                <option value={90}>90%+</option>
              </select>
            </div>

            {/* Metro Access Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Metro Access
              </label>
              <select
                value={filters.metroAccess === null ? '' : filters.metroAccess.toString()}
                onChange={(e) => handleFilterChange('metroAccess', e.target.value === '' ? null : e.target.value === 'true')}
                className="w-full border border-gray-300 rounded-md px-2 py-1 text-sm"
              >
                <option value="">Any</option>
                <option value="true">Metro Accessible</option>
                <option value="false">No Metro Access</option>
              </select>
            </div>
          </div>

          <div className="mt-3 pt-3 border-t border-gray-200">
            <p className="text-xs text-gray-600">
              Showing {filteredColleges.length} of {colleges.length} colleges
            </p>
          </div>
        </div>
      )}

      {/* College Grid View (Temporary replacement for map) */}
      <div className="p-4 pt-20">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <MapPin className="h-5 w-5 text-blue-600 mr-2" />
            <div>
              <h3 className="font-medium text-blue-900">Interactive Map View</h3>
              <p className="text-sm text-blue-700">
                Map functionality is being loaded. For now, browse colleges in grid view below.
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredColleges.map((college) => {
            const coordinates = parseCoordinates(college.coordinates);
            const isSelected = college.id === selectedCollegeId;

            return (
              <div
                key={college.id}
                className={`bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer ${
                  isSelected ? 'ring-2 ring-primary-500' : ''
                }`}
                onClick={() => handleCollegeClick(college)}
              >
                <div className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h3 className="font-semibold text-gray-900 text-sm">{college.name}</h3>
                      <p className="text-primary-600 text-xs">{college.acronym}</p>
                    </div>
                    <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded-full text-xs">
                      #{college.ranking}
                    </span>
                  </div>
                  
                  <div className="space-y-2 text-xs text-gray-600 mb-3">
                    <div className="flex items-center">
                      <Star className="h-3 w-3 mr-1" />
                      <span>{college.placementRate}% Placement Rate</span>
                    </div>
                    <div className="flex items-center">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      <span>₹{college.highestPackage} LPA Highest Package</span>
                    </div>
                    {college.metroAccess && (
                      <div className="flex items-center">
                        <MapPin className="h-3 w-3 mr-1" />
                        <span>Metro Accessible</span>
                      </div>
                    )}
                    {userLocation && college.distance && (
                      <div className="text-primary-600">
                        Distance: {college.distance}km
                      </div>
                    )}
                    {coordinates && (
                      <div className="text-gray-500">
                        Location: {coordinates.lat.toFixed(4)}, {coordinates.lng.toFixed(4)}
                      </div>
                    )}
                  </div>

                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      window.open(`/colleges/${college.id}`, '_blank');
                    }}
                    className="w-full bg-primary-600 text-white px-3 py-2 rounded text-sm hover:bg-primary-700 transition-colors flex items-center justify-center"
                  >
                    <ExternalLink className="h-3 w-3 mr-1" />
                    View Details
                  </button>
                </div>
              </div>
            );
          })}
        </div>

        {filteredColleges.length === 0 && (
          <div className="text-center py-12">
            <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No colleges found</h3>
            <p className="text-gray-600 mb-4">
              Try adjusting your filters or search criteria.
            </p>
            <button
              onClick={clearFilters}
              className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              Clear Filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
