/**
 * Camera Utilities for Document Scanning and OCR
 * Handles camera access, image capture, compression, and text extraction
 */

// OCR API configuration (using Tesseract.js for client-side OCR)
const OCR_CONFIG = {
  language: 'eng',
  options: {
    logger: m => console.log('[OCR]', m)
  }
};

class CameraManager {
  constructor() {
    this.stream = null;
    this.isSupported = false;
    this.constraints = {
      video: {
        facingMode: 'environment', // Use back camera
        width: { ideal: 1920 },
        height: { ideal: 1080 }
      }
    };
    this.checkSupport();
  }

  // Check if camera is supported
  checkSupport() {
    this.isSupported = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
    return this.isSupported;
  }

  // Request camera permission and start stream
  async startCamera(videoElement, options = {}) {
    if (!this.isSupported) {
      throw new Error('Camera not supported in this browser');
    }

    try {
      // Merge custom constraints
      const constraints = {
        ...this.constraints,
        video: {
          ...this.constraints.video,
          ...options
        }
      };

      this.stream = await navigator.mediaDevices.getUserMedia(constraints);
      
      if (videoElement) {
        videoElement.srcObject = this.stream;
        videoElement.play();
      }

      console.log('[Camera] Camera started successfully');
      return this.stream;
    } catch (error) {
      console.error('[Camera] Error starting camera:', error);
      throw this.handleCameraError(error);
    }
  }

  // Stop camera stream
  stopCamera() {
    if (this.stream) {
      this.stream.getTracks().forEach(track => {
        track.stop();
      });
      this.stream = null;
      console.log('[Camera] Camera stopped');
    }
  }

  // Capture image from video stream
  captureImage(videoElement, quality = 0.8) {
    if (!videoElement || !this.stream) {
      throw new Error('Camera not active');
    }

    try {
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      
      // Set canvas dimensions to match video
      canvas.width = videoElement.videoWidth;
      canvas.height = videoElement.videoHeight;
      
      // Draw video frame to canvas
      context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
      
      // Convert to blob
      return new Promise((resolve) => {
        canvas.toBlob(resolve, 'image/jpeg', quality);
      });
    } catch (error) {
      console.error('[Camera] Error capturing image:', error);
      throw error;
    }
  }

  // Capture image as data URL
  captureImageAsDataURL(videoElement, quality = 0.8) {
    if (!videoElement || !this.stream) {
      throw new Error('Camera not active');
    }

    try {
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      
      canvas.width = videoElement.videoWidth;
      canvas.height = videoElement.videoHeight;
      
      context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
      
      return canvas.toDataURL('image/jpeg', quality);
    } catch (error) {
      console.error('[Camera] Error capturing image as data URL:', error);
      throw error;
    }
  }

  // Handle camera errors
  handleCameraError(error) {
    switch (error.name) {
      case 'NotAllowedError':
        return new Error('Camera permission denied. Please allow camera access and try again.');
      case 'NotFoundError':
        return new Error('No camera found on this device.');
      case 'NotSupportedError':
        return new Error('Camera not supported in this browser.');
      case 'OverconstrainedError':
        return new Error('Camera constraints not supported.');
      default:
        return new Error(`Camera error: ${error.message}`);
    }
  }

  // Get available cameras
  async getAvailableCameras() {
    if (!this.isSupported) return [];

    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      return devices.filter(device => device.kind === 'videoinput');
    } catch (error) {
      console.error('[Camera] Error getting available cameras:', error);
      return [];
    }
  }

  // Switch camera (front/back)
  async switchCamera(videoElement, facingMode = 'environment') {
    this.stopCamera();
    
    const newConstraints = {
      ...this.constraints,
      video: {
        ...this.constraints.video,
        facingMode
      }
    };

    return this.startCamera(videoElement, newConstraints.video);
  }
}

// Image compression utilities
export const compressImage = (file, maxWidth = 1920, maxHeight = 1080, quality = 0.8) => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }

      // Set canvas dimensions
      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      context.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(resolve, 'image/jpeg', quality);
    };

    img.src = URL.createObjectURL(file);
  });
};

// OCR text extraction
export const extractTextFromImage = async (imageFile) => {
  try {
    // Dynamic import of Tesseract.js to reduce bundle size
    const Tesseract = await import('tesseract.js');
    
    console.log('[OCR] Starting text extraction...');
    
    const { data: { text, confidence } } = await Tesseract.recognize(
      imageFile,
      OCR_CONFIG.language,
      OCR_CONFIG.options
    );

    console.log('[OCR] Text extraction completed. Confidence:', confidence);
    
    return {
      text: text.trim(),
      confidence,
      success: true
    };
  } catch (error) {
    console.error('[OCR] Error extracting text:', error);
    return {
      text: '',
      confidence: 0,
      success: false,
      error: error.message
    };
  }
};

// Document type detection
export const detectDocumentType = (text) => {
  const lowerText = text.toLowerCase();
  
  // Common patterns for different document types
  const patterns = {
    transcript: [
      'transcript', 'grade', 'marks', 'semester', 'cgpa', 'gpa',
      'course', 'subject', 'credit', 'university', 'college'
    ],
    marksheet: [
      'marksheet', 'mark sheet', 'examination', 'result', 'percentage',
      'total marks', 'obtained marks', 'grade', 'division'
    ],
    certificate: [
      'certificate', 'certify', 'awarded', 'completion', 'achievement',
      'diploma', 'degree', 'bachelor', 'master'
    ],
    scorecard: [
      'score', 'test', 'sat', 'gre', 'gmat', 'toefl', 'ielts',
      'percentile', 'verbal', 'quantitative', 'analytical'
    ]
  };

  let bestMatch = { type: 'unknown', confidence: 0 };

  Object.entries(patterns).forEach(([type, keywords]) => {
    const matches = keywords.filter(keyword => lowerText.includes(keyword));
    const confidence = matches.length / keywords.length;
    
    if (confidence > bestMatch.confidence) {
      bestMatch = { type, confidence };
    }
  });

  return bestMatch;
};

// Extract specific data from documents
export const extractDocumentData = (text, documentType) => {
  const data = {
    type: documentType,
    extractedData: {},
    rawText: text
  };

  switch (documentType) {
    case 'transcript':
      data.extractedData = extractTranscriptData(text);
      break;
    case 'marksheet':
      data.extractedData = extractMarksheetData(text);
      break;
    case 'certificate':
      data.extractedData = extractCertificateData(text);
      break;
    case 'scorecard':
      data.extractedData = extractScorecardData(text);
      break;
    default:
      data.extractedData = { text };
  }

  return data;
};

// Extract transcript-specific data
const extractTranscriptData = (text) => {
  const data = {};
  
  // Extract CGPA/GPA
  const cgpaMatch = text.match(/cgpa[:\s]*(\d+\.?\d*)/i);
  if (cgpaMatch) data.cgpa = parseFloat(cgpaMatch[1]);
  
  const gpaMatch = text.match(/gpa[:\s]*(\d+\.?\d*)/i);
  if (gpaMatch) data.gpa = parseFloat(gpaMatch[1]);
  
  // Extract university/college name
  const universityMatch = text.match(/(university|college|institute)[:\s]*([^\n]+)/i);
  if (universityMatch) data.institution = universityMatch[2].trim();
  
  // Extract degree
  const degreeMatch = text.match(/(bachelor|master|diploma)[^\n]*/i);
  if (degreeMatch) data.degree = degreeMatch[0].trim();
  
  return data;
};

// Extract marksheet-specific data
const extractMarksheetData = (text) => {
  const data = {};
  
  // Extract percentage
  const percentageMatch = text.match(/(\d+\.?\d*)%/);
  if (percentageMatch) data.percentage = parseFloat(percentageMatch[1]);
  
  // Extract total marks
  const totalMarksMatch = text.match(/total[:\s]*(\d+)/i);
  if (totalMarksMatch) data.totalMarks = parseInt(totalMarksMatch[1]);
  
  // Extract obtained marks
  const obtainedMarksMatch = text.match(/obtained[:\s]*(\d+)/i);
  if (obtainedMarksMatch) data.obtainedMarks = parseInt(obtainedMarksMatch[1]);
  
  return data;
};

// Extract certificate-specific data
const extractCertificateData = (text) => {
  const data = {};
  
  // Extract certificate name
  const certMatch = text.match(/certificate[^\n]*/i);
  if (certMatch) data.certificateName = certMatch[0].trim();
  
  // Extract date
  const dateMatch = text.match(/\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}/);
  if (dateMatch) data.date = dateMatch[0];
  
  return data;
};

// Extract scorecard-specific data
const extractScorecardData = (text) => {
  const data = {};
  
  // Extract scores
  const scoreMatches = text.match(/(\d{2,4})/g);
  if (scoreMatches) {
    data.scores = scoreMatches.map(score => parseInt(score));
  }
  
  // Extract percentile
  const percentileMatch = text.match(/(\d+)(?:st|nd|rd|th)?\s*percentile/i);
  if (percentileMatch) data.percentile = parseInt(percentileMatch[1]);
  
  return data;
};

// Create singleton instance
const cameraManager = new CameraManager();

export default cameraManager;
