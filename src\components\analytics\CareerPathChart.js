'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, Treemap } from 'recharts';
import { Building2, TrendingUp, Users, Filter, Briefcase, ArrowRight } from 'lucide-react';
import AnalyticsCard from './AnalyticsCard';

const CareerPathChart = ({ 
  colleges = [], 
  selectedCollege = null,
  onCollegeSelect = () => {} 
}) => {
  const [viewType, setViewType] = useState('companies'); // companies, industries, progression
  const [selectedTier, setSelectedTier] = useState('all');
  const [loading, setLoading] = useState(false);

  // Process career path data
  const processCareerData = () => {
    if (!colleges.length) return { companies: [], industries: [], progression: [] };

    const college = selectedCollege ? 
      colleges.find(c => c.id === selectedCollege) : 
      colleges[0];

    if (!college) return { companies: [], industries: [], progression: [] };

    const analytics = college.analytics || college;
    let careerPaths = analytics.careerPaths || [];

    // Filter by tier if selected
    if (selectedTier !== 'all') {
      careerPaths = careerPaths.filter(company => 
        company.tier.toLowerCase() === selectedTier.toLowerCase()
      );
    }

    // Process companies data
    const companies = careerPaths.map(company => ({
      ...company,
      salaryRange: `₹${Math.round(company.avgSalary * 0.8)}-${Math.round(company.avgSalary * 1.2)}L`,
      growthCategory: company.growthRate >= 15 ? 'High' : company.growthRate >= 10 ? 'Medium' : 'Low'
    })).sort((a, b) => b.placementCount - a.placementCount);

    // Process industries data
    const industries = {};
    careerPaths.forEach(company => {
      const industry = getIndustryFromCompany(company.name);
      if (!industries[industry]) {
        industries[industry] = {
          name: industry,
          count: 0,
          avgSalary: 0,
          companies: [],
          totalSalary: 0
        };
      }
      industries[industry].count += company.placementCount;
      industries[industry].totalSalary += company.avgSalary * company.placementCount;
      industries[industry].companies.push(company.name);
    });

    const industriesArray = Object.values(industries).map(industry => ({
      ...industry,
      avgSalary: Math.round(industry.totalSalary / industry.count),
      companies: [...new Set(industry.companies)]
    })).sort((a, b) => b.count - a.count);

    // Process career progression data
    const progression = generateCareerProgression(careerPaths);

    return { companies, industries: industriesArray, progression };
  };

  // Map company to industry
  const getIndustryFromCompany = (companyName) => {
    const industryMap = {
      'Microsoft': 'Technology',
      'Google': 'Technology',
      'Amazon': 'Technology',
      'Flipkart': 'E-commerce',
      'Goldman Sachs': 'Finance',
      'TCS': 'IT Services',
      'Infosys': 'IT Services',
      'Wipro': 'IT Services',
      'Accenture': 'Consulting',
      'Capgemini': 'IT Services',
      'L&T': 'Engineering',
      'Bosch': 'Automotive',
      'Siemens': 'Engineering',
      'Honeywell': 'Engineering'
    };
    return industryMap[companyName] || 'Other';
  };

  // Generate career progression data
  const generateCareerProgression = (careerPaths) => {
    const tiers = ['Entry Level', 'Mid Level', 'Senior Level', 'Leadership'];
    const tierData = tiers.map((tier, index) => {
      const multiplier = 1 + (index * 0.8); // Salary growth multiplier
      const totalPlacements = careerPaths.reduce((sum, company) => sum + company.placementCount, 0);
      const tierPlacements = Math.round(totalPlacements * (index === 0 ? 0.6 : index === 1 ? 0.25 : index === 2 ? 0.12 : 0.03));
      
      return {
        tier,
        level: index + 1,
        placements: tierPlacements,
        avgSalary: Math.round(careerPaths.reduce((sum, company) => sum + company.avgSalary, 0) / careerPaths.length * multiplier),
        experience: index === 0 ? '0-2 years' : index === 1 ? '3-6 years' : index === 2 ? '7-12 years' : '12+ years'
      };
    });

    return tierData;
  };

  const { companies, industries, progression } = processCareerData();

  // Chart colors
  const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'];

  // Custom tooltips
  const CompanyTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-900 mb-2">{label}</p>
          <div className="space-y-1 text-sm">
            <p>Placements: <span className="font-medium">{data.placementCount}</span></p>
            <p>Avg Salary: <span className="font-medium">₹{data.avgSalary}L</span></p>
            <p>Growth Rate: <span className="font-medium">{data.growthRate}%</span></p>
            <p>Tier: <span className="font-medium">{data.tier}</span></p>
          </div>
        </div>
      );
    }
    return null;
  };

  const IndustryTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-900 mb-2">{label}</p>
          <div className="space-y-1 text-sm">
            <p>Total Placements: <span className="font-medium">{data.count}</span></p>
            <p>Avg Salary: <span className="font-medium">₹{data.avgSalary}L</span></p>
            <p>Companies: <span className="font-medium">{data.companies.length}</span></p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <AnalyticsCard
      title="Career Path Visualization"
      subtitle="Explore career opportunities and progression patterns"
      info="Analyze placement patterns, industry distribution, and career growth paths"
      loading={loading}
      className="col-span-full"
    >
      <div className="space-y-6">
        {/* Controls */}
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <select
                value={selectedTier}
                onChange={(e) => setSelectedTier(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="all">All Tiers</option>
                <option value="tier1">Tier 1</option>
                <option value="tier2">Tier 2</option>
                <option value="core">Core Engineering</option>
              </select>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewType('companies')}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                viewType === 'companies' 
                  ? 'bg-primary-600 text-white' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <Building2 className="h-4 w-4 inline mr-1" />
              Companies
            </button>
            <button
              onClick={() => setViewType('industries')}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                viewType === 'industries' 
                  ? 'bg-primary-600 text-white' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <Briefcase className="h-4 w-4 inline mr-1" />
              Industries
            </button>
            <button
              onClick={() => setViewType('progression')}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                viewType === 'progression' 
                  ? 'bg-primary-600 text-white' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <TrendingUp className="h-4 w-4 inline mr-1" />
              Progression
            </button>
          </div>
        </div>

        {/* Companies View */}
        {viewType === 'companies' && companies.length > 0 && (
          <>
            <div className="h-80">
              <h4 className="font-medium text-gray-900 mb-4">Company-wise Placements</h4>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={companies.slice(0, 12)}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis 
                    dataKey="name" 
                    stroke="#6b7280"
                    fontSize={12}
                    angle={-45}
                    textAnchor="end"
                    height={80}
                  />
                  <YAxis 
                    stroke="#6b7280"
                    fontSize={12}
                    label={{ value: 'Placements', angle: -90, position: 'insideLeft' }}
                  />
                  <Tooltip content={<CompanyTooltip />} />
                  <Bar dataKey="placementCount" radius={[4, 4, 0, 0]}>
                    {companies.slice(0, 12).map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </div>

            {/* Company Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {companies.slice(0, 9).map((company, index) => (
                <div key={company.name} className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h5 className="font-semibold text-gray-900">{company.name}</h5>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      company.tier === 'Tier1' ? 'bg-green-100 text-green-800' :
                      company.tier === 'Tier2' ? 'bg-blue-100 text-blue-800' :
                      'bg-purple-100 text-purple-800'
                    }`}>
                      {company.tier}
                    </span>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Placements:</span>
                      <span className="font-medium">{company.placementCount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Avg Salary:</span>
                      <span className="font-medium">₹{company.avgSalary}L</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Growth Rate:</span>
                      <span className={`font-medium ${
                        company.growthRate >= 15 ? 'text-green-600' :
                        company.growthRate >= 10 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {company.growthRate}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Salary Range:</span>
                      <span className="font-medium text-xs">{company.salaryRange}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}

        {/* Industries View */}
        {viewType === 'industries' && industries.length > 0 && (
          <>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Industry Distribution Pie Chart */}
              <div className="h-80">
                <h4 className="font-medium text-gray-900 mb-4">Industry Distribution</h4>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={industries}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {industries.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>

              {/* Industry Salary Comparison */}
              <div className="h-80">
                <h4 className="font-medium text-gray-900 mb-4">Average Salary by Industry</h4>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={industries}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis 
                      dataKey="name" 
                      stroke="#6b7280"
                      fontSize={12}
                      angle={-45}
                      textAnchor="end"
                      height={80}
                    />
                    <YAxis 
                      stroke="#6b7280"
                      fontSize={12}
                      label={{ value: 'Salary (LPA)', angle: -90, position: 'insideLeft' }}
                    />
                    <Tooltip content={<IndustryTooltip />} />
                    <Bar dataKey="avgSalary" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Industry Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {industries.map((industry, index) => (
                <div key={industry.name} className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h5 className="font-semibold text-gray-900">{industry.name}</h5>
                    <div className="text-right">
                      <p className="text-lg font-bold text-primary-600">{industry.count}</p>
                      <p className="text-xs text-gray-500">placements</p>
                    </div>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Avg Salary:</span>
                      <span className="font-medium">₹{industry.avgSalary}L</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Companies:</span>
                      <span className="font-medium">{industry.companies.length}</span>
                    </div>
                    <div className="mt-2">
                      <span className="text-gray-600 text-xs">Top Companies:</span>
                      <p className="text-xs text-gray-800 mt-1">
                        {industry.companies.slice(0, 3).join(', ')}
                        {industry.companies.length > 3 && ` +${industry.companies.length - 3} more`}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}

        {/* Career Progression View */}
        {viewType === 'progression' && progression.length > 0 && (
          <>
            <div className="h-80">
              <h4 className="font-medium text-gray-900 mb-4">Career Progression Path</h4>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={progression}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis 
                    dataKey="tier" 
                    stroke="#6b7280"
                    fontSize={12}
                  />
                  <YAxis 
                    yAxisId="left"
                    stroke="#6b7280"
                    fontSize={12}
                    label={{ value: 'Placements', angle: -90, position: 'insideLeft' }}
                  />
                  <YAxis 
                    yAxisId="right"
                    orientation="right"
                    stroke="#6b7280"
                    fontSize={12}
                    label={{ value: 'Salary (LPA)', angle: 90, position: 'insideRight' }}
                  />
                  <Tooltip />
                  <Bar yAxisId="left" dataKey="placements" fill="#3b82f6" name="Placements" />
                  <Bar yAxisId="right" dataKey="avgSalary" fill="#10b981" name="Avg Salary" />
                </BarChart>
              </ResponsiveContainer>
            </div>

            {/* Progression Flow */}
            <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-6">
              <h4 className="font-medium text-gray-900 mb-4">Typical Career Progression</h4>
              <div className="flex items-center justify-between">
                {progression.map((level, index) => (
                  <div key={level.tier} className="flex items-center">
                    <div className="text-center">
                      <div className={`w-16 h-16 rounded-full flex items-center justify-center text-white font-bold text-sm ${
                        index === 0 ? 'bg-blue-500' :
                        index === 1 ? 'bg-green-500' :
                        index === 2 ? 'bg-yellow-500' : 'bg-purple-500'
                      }`}>
                        {level.placements}
                      </div>
                      <p className="font-medium text-gray-900 mt-2 text-sm">{level.tier}</p>
                      <p className="text-xs text-gray-600">{level.experience}</p>
                      <p className="text-xs font-medium text-gray-800">₹{level.avgSalary}L</p>
                    </div>
                    {index < progression.length - 1 && (
                      <ArrowRight className="h-6 w-6 text-gray-400 mx-4" />
                    )}
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        {/* College Selection */}
        {colleges.length > 1 && (
          <div className="border-t pt-4">
            <h4 className="font-medium text-gray-900 mb-3">Select College:</h4>
            <div className="flex flex-wrap gap-2">
              {colleges.slice(0, 8).map(college => (
                <button
                  key={college.id}
                  onClick={() => onCollegeSelect(college.id)}
                  className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    selectedCollege === college.id
                      ? 'bg-primary-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {college.acronym}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </AnalyticsCard>
  );
};

export default CareerPathChart;
