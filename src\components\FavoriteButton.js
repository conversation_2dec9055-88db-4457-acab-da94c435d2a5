'use client';

import { useState } from 'react';
import { Heart, Loader2 } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useFavorites } from '../hooks/useFavorites';

const FavoriteButton = ({ 
  collegeId, 
  size = 'md', 
  variant = 'icon', 
  className = '',
  onAuthRequired = null 
}) => {
  const { user } = useAuth();
  const { checkIsFavorite, toggleFavorite } = useFavorites();
  const [isLoading, setIsLoading] = useState(false);
  
  const isFavorite = checkIsFavorite(collegeId);

  const handleClick = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!user) {
      if (onAuthRequired) {
        onAuthRequired();
      } else {
        alert('Please sign in to save favorites');
      }
      return;
    }
    
    setIsLoading(true);
    try {
      await toggleFavorite(collegeId);
    } catch (error) {
      console.error('Error toggling favorite:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const sizeClasses = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
    xl: 'w-6 h-6'
  };

  const buttonSizeClasses = {
    sm: 'p-1',
    md: 'p-2',
    lg: 'p-2.5',
    xl: 'p-3'
  };

  if (variant === 'button') {
    return (
      <button
        onClick={handleClick}
        disabled={isLoading}
        className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed ${
          isFavorite
            ? 'bg-red-50 text-red-600 hover:bg-red-100 border border-red-200'
            : 'bg-gray-50 text-gray-600 hover:bg-red-50 hover:text-red-600 border border-gray-200 hover:border-red-200'
        } ${className}`}
      >
        {isLoading ? (
          <Loader2 className={`${sizeClasses[size]} animate-spin`} />
        ) : (
          <Heart className={`${sizeClasses[size]} ${isFavorite ? 'fill-current' : ''}`} />
        )}
        <span>{isFavorite ? 'Saved' : 'Save'}</span>
      </button>
    );
  }

  return (
    <button
      onClick={handleClick}
      disabled={isLoading}
      className={`${buttonSizeClasses[size]} rounded-full shadow-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed ${
        isFavorite 
          ? 'bg-red-500 text-white hover:bg-red-600' 
          : 'bg-white text-gray-400 hover:text-red-500 hover:bg-red-50'
      } ${className}`}
      title={isFavorite ? 'Remove from favorites' : 'Add to favorites'}
    >
      {isLoading ? (
        <Loader2 className={`${sizeClasses[size]} animate-spin`} />
      ) : (
        <Heart className={`${sizeClasses[size]} ${isFavorite ? 'fill-current' : ''}`} />
      )}
    </button>
  );
};

export default FavoriteButton;
