'use client';

import { useState, useEffect } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { Calculator, DollarSign, TrendingUp, Clock, AlertCircle } from 'lucide-react';
import AnalyticsCard from './AnalyticsCard';
import { calculateROI } from '../../lib/analyticsData';

const ROICalculator = ({ 
  colleges = [], 
  selectedCollege = null,
  onCollegeSelect = () => {} 
}) => {
  const [fees, setFees] = useState(400000);
  const [startingSalary, setStartingSalary] = useState(800000);
  const [years, setYears] = useState(5);
  const [roiData, setRoiData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [compareMode, setCompareMode] = useState(false);

  // Calculate ROI when inputs change
  useEffect(() => {
    if (fees > 0 && startingSalary > 0 && years > 0) {
      setLoading(true);
      setTimeout(() => {
        const result = calculateROI(fees, startingSalary, years);
        setRoiData(result);
        setLoading(false);
      }, 300);
    }
  }, [fees, startingSalary, years]);

  // Get college-specific data
  const getCollegeData = (college) => {
    const analytics = college.analytics || college;
    const totalFees = analytics.roiData?.totalFees || (200000 + college.ranking * 10000);
    const avgSalary = analytics.roiData?.averageStartingSalary || (college.highestPackage * 0.6 * 100000);
    
    return {
      name: college.acronym,
      fullName: college.name,
      fees: totalFees,
      salary: avgSalary,
      roi: calculateROI(totalFees, avgSalary, years)
    };
  };

  // Compare multiple colleges
  const getCollegeComparison = () => {
    if (!colleges.length) return [];
    
    return colleges.slice(0, 8).map(getCollegeData).sort((a, b) => b.roi.roiPercentage - a.roi.roiPercentage);
  };

  const collegeComparison = getCollegeComparison();

  // Format currency for display
  const formatCurrency = (amount) => {
    if (amount >= 10000000) return `₹${(amount / 10000000).toFixed(1)}Cr`;
    if (amount >= 100000) return `₹${(amount / 100000).toFixed(1)}L`;
    return `₹${amount.toLocaleString()}`;
  };

  // Custom tooltip for ROI chart
  const ROITooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-900 mb-2">Year {label}</p>
          <div className="space-y-1 text-sm">
            <p>Salary: <span className="font-medium">{formatCurrency(data.salary)}</span></p>
            <p>Net Salary: <span className="font-medium">{formatCurrency(data.netSalary)}</span></p>
            <p>Disposable Income: <span className="font-medium">{formatCurrency(data.disposableIncome)}</span></p>
            <p>Cumulative ROI: <span className={`font-medium ${data.cumulativeROI >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {formatCurrency(data.cumulativeROI)}
            </span></p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <AnalyticsCard
      title="ROI Calculator"
      subtitle="Calculate return on investment for your engineering education"
      info="Analyze the financial returns of your college investment over time"
      loading={loading && !roiData}
      className="col-span-full"
    >
      <div className="space-y-6">
        {/* Toggle between calculator and comparison */}
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setCompareMode(false)}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              !compareMode 
                ? 'bg-primary-600 text-white' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Calculator className="h-4 w-4 inline mr-2" />
            Calculator
          </button>
          <button
            onClick={() => setCompareMode(true)}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              compareMode 
                ? 'bg-primary-600 text-white' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <TrendingUp className="h-4 w-4 inline mr-2" />
            Compare Colleges
          </button>
        </div>

        {!compareMode ? (
          <>
            {/* Input Controls */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Total Fees (₹)
                </label>
                <input
                  type="number"
                  value={fees}
                  onChange={(e) => setFees(Number(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  min="0"
                  step="10000"
                />
                <p className="text-xs text-gray-500 mt-1">Including all 4-year expenses</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Starting Salary (₹/year)
                </label>
                <input
                  type="number"
                  value={startingSalary}
                  onChange={(e) => setStartingSalary(Number(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  min="0"
                  step="50000"
                />
                <p className="text-xs text-gray-500 mt-1">Expected first job salary</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Analysis Period (years)
                </label>
                <select
                  value={years}
                  onChange={(e) => setYears(Number(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value={3}>3 years</option>
                  <option value={5}>5 years</option>
                  <option value={7}>7 years</option>
                  <option value={10}>10 years</option>
                </select>
                <p className="text-xs text-gray-500 mt-1">ROI calculation period</p>
              </div>
            </div>

            {/* ROI Results */}
            {roiData && (
              <>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-primary-50 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <TrendingUp className="h-5 w-5 text-primary-600" />
                      <span className="text-sm font-medium text-primary-700">ROI</span>
                    </div>
                    <p className={`text-2xl font-bold ${roiData.roiPercentage >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {roiData.roiPercentage}%
                    </p>
                    <p className="text-xs text-primary-600">{years}-year return</p>
                  </div>
                  
                  <div className="bg-green-50 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <DollarSign className="h-5 w-5 text-green-600" />
                      <span className="text-sm font-medium text-green-700">Net Gain</span>
                    </div>
                    <p className={`text-lg font-bold ${roiData.netROI >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatCurrency(roiData.netROI)}
                    </p>
                    <p className="text-xs text-green-600">After {years} years</p>
                  </div>
                  
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Clock className="h-5 w-5 text-blue-600" />
                      <span className="text-sm font-medium text-blue-700">Break-even</span>
                    </div>
                    <p className="text-lg font-bold text-blue-600">
                      {roiData.breakEvenMonths > 0 ? `${Math.round(roiData.breakEvenMonths)}` : 'N/A'}
                    </p>
                    <p className="text-xs text-blue-600">
                      {roiData.breakEvenMonths > 0 ? 'months' : 'Not profitable'}
                    </p>
                  </div>
                  
                  <div className="bg-purple-50 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Calculator className="h-5 w-5 text-purple-600" />
                      <span className="text-sm font-medium text-purple-700">Total Earnings</span>
                    </div>
                    <p className="text-lg font-bold text-purple-600">
                      {formatCurrency(roiData.totalEarnings)}
                    </p>
                    <p className="text-xs text-purple-600">Over {years} years</p>
                  </div>
                </div>

                {/* ROI Timeline Chart */}
                <div className="h-80">
                  <h4 className="font-medium text-gray-900 mb-4">ROI Timeline</h4>
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={roiData.yearlyBreakdown}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis 
                        dataKey="year" 
                        stroke="#6b7280"
                        fontSize={12}
                      />
                      <YAxis 
                        stroke="#6b7280"
                        fontSize={12}
                        tickFormatter={(value) => formatCurrency(value)}
                      />
                      <Tooltip content={<ROITooltip />} />
                      <Line
                        type="monotone"
                        dataKey="cumulativeROI"
                        stroke="#3b82f6"
                        strokeWidth={3}
                        dot={{ r: 5 }}
                        activeDot={{ r: 7 }}
                      />
                      <Line
                        type="monotone"
                        dataKey="salary"
                        stroke="#10b981"
                        strokeWidth={2}
                        strokeDasharray="5 5"
                        dot={{ r: 3 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>

                {/* Assumptions */}
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-start space-x-2">
                    <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-yellow-800 mb-2">Assumptions</h4>
                      <ul className="text-sm text-yellow-700 space-y-1">
                        <li>• Annual salary growth: 12%</li>
                        <li>• Effective tax rate: 30%</li>
                        <li>• Annual living expenses: ₹1.8L (Bangalore)</li>
                        <li>• Inflation not considered in salary growth</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </>
            )}
          </>
        ) : (
          /* College Comparison Mode */
          <div className="space-y-6">
            <div className="h-80">
              <h4 className="font-medium text-gray-900 mb-4">College ROI Comparison ({years} years)</h4>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={collegeComparison}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis 
                    dataKey="name" 
                    stroke="#6b7280"
                    fontSize={12}
                    angle={-45}
                    textAnchor="end"
                    height={80}
                  />
                  <YAxis 
                    stroke="#6b7280"
                    fontSize={12}
                    label={{ value: 'ROI (%)', angle: -90, position: 'insideLeft' }}
                  />
                  <Tooltip 
                    formatter={(value, name) => [`${value}%`, 'ROI']}
                    labelFormatter={(label) => `College: ${label}`}
                  />
                  <Bar dataKey="roi.roiPercentage" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </div>

            {/* College ROI Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {collegeComparison.slice(0, 6).map((college, index) => (
                <div key={college.name} className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-semibold text-gray-900">{college.name}</h4>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      college.roi.roiPercentage >= 200 ? 'bg-green-100 text-green-800' :
                      college.roi.roiPercentage >= 100 ? 'bg-blue-100 text-blue-800' :
                      college.roi.roiPercentage >= 0 ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {college.roi.roiPercentage}% ROI
                    </span>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Fees:</span>
                      <span className="font-medium">{formatCurrency(college.fees)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Starting Salary:</span>
                      <span className="font-medium">{formatCurrency(college.salary)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Break-even:</span>
                      <span className="font-medium">
                        {college.roi.breakEvenMonths > 0 ? `${Math.round(college.roi.breakEvenMonths)}m` : 'N/A'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Net Gain:</span>
                      <span className={`font-medium ${college.roi.netROI >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {formatCurrency(college.roi.netROI)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </AnalyticsCard>
  );
};

export default ROICalculator;
