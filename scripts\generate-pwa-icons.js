/**
 * Generate PWA Icons Script
 * Creates placeholder SVG icons for PWA functionality
 */

const fs = require('fs');
const path = require('path');

// Icon sizes required for PWA
const iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];

// Create icons directory if it doesn't exist
const iconsDir = path.join(process.cwd(), 'public', 'icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// Generate SVG icon template
const generateSVGIcon = (size) => {
  return `<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="${size}" height="${size}" rx="${size * 0.15}" fill="#2563eb"/>
  
  <!-- Education/College Icon -->
  <g transform="translate(${size * 0.2}, ${size * 0.2}) scale(${size * 0.6 / 100})">
    <!-- Building base -->
    <rect x="10" y="60" width="80" height="30" fill="white" opacity="0.9"/>
    
    <!-- Columns -->
    <rect x="15" y="40" width="8" height="50" fill="white"/>
    <rect x="30" y="40" width="8" height="50" fill="white"/>
    <rect x="45" y="40" width="8" height="50" fill="white"/>
    <rect x="60" y="40" width="8" height="50" fill="white"/>
    <rect x="75" y="40" width="8" height="50" fill="white"/>
    
    <!-- Roof -->
    <polygon points="5,40 50,20 95,40" fill="white"/>
    
    <!-- Door -->
    <rect x="42" y="70" width="16" height="20" fill="#2563eb"/>
    
    <!-- Steps -->
    <rect x="20" y="85" width="60" height="3" fill="white" opacity="0.7"/>
    <rect x="25" y="88" width="50" height="2" fill="white" opacity="0.5"/>
  </g>
  
  <!-- App name indicator -->
  <text x="${size/2}" y="${size * 0.9}" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="${size * 0.08}" font-weight="bold">BEC</text>
</svg>`;
};

// Generate PNG conversion function (simplified - creates SVG files)
const generateIcon = (size) => {
  const svgContent = generateSVGIcon(size);
  const filename = `icon-${size}x${size}.svg`;
  const filepath = path.join(iconsDir, filename);
  
  fs.writeFileSync(filepath, svgContent);
  console.log(`Generated: ${filename}`);
  
  // Also create PNG filename for manifest compatibility
  const pngFilename = `icon-${size}x${size}.png`;
  const pngFilepath = path.join(iconsDir, pngFilename);
  
  // For now, copy SVG as PNG (browsers will handle SVG in manifest)
  fs.writeFileSync(pngFilepath, svgContent);
  console.log(`Generated: ${pngFilename}`);
};

// Generate shortcut icons
const generateShortcutIcon = (name, iconPath) => {
  const size = 96;
  let iconContent = '';
  
  switch (name) {
    case 'compare':
      iconContent = `
        <rect x="20" y="30" width="25" height="40" fill="white" rx="3"/>
        <rect x="55" y="30" width="25" height="40" fill="white" rx="3"/>
        <path d="M35 45 L45 40 L45 50 Z" fill="#2563eb"/>
        <path d="M65 45 L55 40 L55 50 Z" fill="#2563eb"/>
      `;
      break;
    case 'browse':
      iconContent = `
        <circle cx="35" cy="35" r="15" stroke="white" stroke-width="3" fill="none"/>
        <path d="M46 46 L60 60" stroke="white" stroke-width="3" stroke-linecap="round"/>
        <rect x="20" y="65" width="60" height="3" fill="white" rx="1"/>
        <rect x="25" y="72" width="50" height="3" fill="white" rx="1"/>
        <rect x="30" y="79" width="40" height="3" fill="white" rx="1"/>
      `;
      break;
    case 'analytics':
      iconContent = `
        <rect x="20" y="60" width="8" height="20" fill="white"/>
        <rect x="35" y="45" width="8" height="35" fill="white"/>
        <rect x="50" y="30" width="8" height="50" fill="white"/>
        <rect x="65" y="40" width="8" height="40" fill="white"/>
        <path d="M25 45 L40 30 L55 35 L70 25" stroke="white" stroke-width="2" fill="none"/>
      `;
      break;
  }
  
  const svgContent = `<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="${size}" height="${size}" rx="${size * 0.15}" fill="#2563eb"/>
    ${iconContent}
  </svg>`;
  
  const filename = `shortcut-${name}.png`;
  const filepath = path.join(iconsDir, filename);
  
  fs.writeFileSync(filepath, svgContent);
  console.log(`Generated: ${filename}`);
};

// Generate all icons
console.log('Generating PWA icons...');

iconSizes.forEach(generateIcon);

// Generate shortcut icons
generateShortcutIcon('compare', 'compare');
generateShortcutIcon('browse', 'browse');
generateShortcutIcon('analytics', 'analytics');

console.log('\n✅ PWA icons generated successfully!');
console.log('📁 Location: public/icons/');
console.log('📝 Note: For production, replace with high-quality PNG/ICO files');
