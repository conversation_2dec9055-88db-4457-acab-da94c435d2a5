'use client';

import { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { 
  Home, 
  Search, 
  BarChart3, 
  Heart, 
  User, 
  Menu,
  X,
  ChevronRight,
  Bell,
  Settings
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

const TouchNavigation = () => {
  const router = useRouter();
  const pathname = usePathname();
  const { user } = useAuth();
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [activeTab, setActiveTab] = useState('home');

  // Navigation items for bottom tab bar
  const bottomNavItems = [
    {
      id: 'home',
      label: 'Home',
      icon: Home,
      path: '/',
      color: 'text-blue-600'
    },
    {
      id: 'colleges',
      label: 'Browse',
      icon: Search,
      path: '/colleges',
      color: 'text-green-600'
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: BarChart3,
      path: '/analytics',
      color: 'text-purple-600'
    },
    {
      id: 'favorites',
      label: 'Favorites',
      icon: Heart,
      path: '/favorites',
      color: 'text-red-600',
      requireAuth: true
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: User,
      path: '/profile',
      color: 'text-gray-600',
      requireAuth: true
    }
  ];

  // Mobile menu items
  const mobileMenuItems = [
    {
      label: 'Compare Colleges',
      path: '/compare',
      icon: BarChart3,
      description: 'Side-by-side comparison'
    },
    {
      label: 'Notifications',
      path: '/notifications',
      icon: Bell,
      description: 'Manage your alerts',
      requireAuth: true
    },
    {
      label: 'Settings',
      path: '/settings',
      icon: Settings,
      description: 'App preferences',
      requireAuth: true
    }
  ];

  // Update active tab based on current path
  useEffect(() => {
    const currentItem = bottomNavItems.find(item => 
      pathname === item.path || (item.path !== '/' && pathname.startsWith(item.path))
    );
    if (currentItem) {
      setActiveTab(currentItem.id);
    }
  }, [pathname]);

  // Handle navigation
  const handleNavigation = (path, requireAuth = false) => {
    if (requireAuth && !user) {
      // Redirect to login or show auth modal
      router.push('/auth/login');
      return;
    }
    
    router.push(path);
    setShowMobileMenu(false);
    
    // Haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(25);
    }
  };

  // Handle tab press with visual feedback
  const handleTabPress = (item) => {
    // Add pressed state animation
    const element = document.getElementById(`tab-${item.id}`);
    if (element) {
      element.classList.add('scale-95');
      setTimeout(() => {
        element.classList.remove('scale-95');
      }, 150);
    }
    
    handleNavigation(item.path, item.requireAuth);
  };

  return (
    <>
      {/* Bottom Navigation Bar */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-40 safe-area-pb">
        <div className="flex items-center justify-around px-2 py-2">
          {bottomNavItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeTab === item.id;
            const isDisabled = item.requireAuth && !user;
            
            return (
              <button
                key={item.id}
                id={`tab-${item.id}`}
                onClick={() => handleTabPress(item)}
                disabled={isDisabled}
                className={`
                  flex flex-col items-center justify-center p-2 min-w-[44px] min-h-[44px] rounded-lg
                  transition-all duration-200 transform active:scale-95
                  ${isActive 
                    ? `${item.color} bg-blue-50` 
                    : isDisabled 
                      ? 'text-gray-300' 
                      : 'text-gray-500 hover:text-gray-700 active:bg-gray-100'
                  }
                `}
                style={{ touchAction: 'manipulation' }}
              >
                <Icon 
                  className={`w-6 h-6 ${isActive ? 'stroke-2' : 'stroke-1.5'}`} 
                />
                <span className={`text-xs mt-1 font-medium ${isActive ? 'font-semibold' : ''}`}>
                  {item.label}
                </span>
                
                {/* Active indicator */}
                {isActive && (
                  <div className="absolute -top-1 w-1 h-1 bg-blue-600 rounded-full"></div>
                )}
              </button>
            );
          })}
        </div>
      </div>

      {/* Mobile Menu Button (Top Right) */}
      <button
        onClick={() => setShowMobileMenu(!showMobileMenu)}
        className="fixed top-4 right-4 z-50 bg-white rounded-full p-3 shadow-lg border border-gray-200 min-w-[44px] min-h-[44px] flex items-center justify-center md:hidden"
        style={{ touchAction: 'manipulation' }}
      >
        {showMobileMenu ? (
          <X className="w-6 h-6 text-gray-600" />
        ) : (
          <Menu className="w-6 h-6 text-gray-600" />
        )}
      </button>

      {/* Mobile Menu Overlay */}
      {showMobileMenu && (
        <div className="fixed inset-0 z-40 md:hidden">
          {/* Backdrop */}
          <div 
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={() => setShowMobileMenu(false)}
          />
          
          {/* Menu Panel */}
          <div className="absolute top-0 right-0 w-80 max-w-[90vw] h-full bg-white shadow-xl">
            <div className="p-6 pt-20">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">
                Quick Actions
              </h3>
              
              <div className="space-y-2">
                {mobileMenuItems.map((item) => {
                  const Icon = item.icon;
                  const isDisabled = item.requireAuth && !user;
                  
                  return (
                    <button
                      key={item.path}
                      onClick={() => handleNavigation(item.path, item.requireAuth)}
                      disabled={isDisabled}
                      className={`
                        w-full flex items-center space-x-4 p-4 rounded-xl text-left
                        min-h-[60px] transition-all duration-200
                        ${isDisabled 
                          ? 'text-gray-300 cursor-not-allowed' 
                          : 'text-gray-700 hover:bg-gray-50 active:bg-gray-100'
                        }
                      `}
                      style={{ touchAction: 'manipulation' }}
                    >
                      <div className="flex-shrink-0 w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                        <Icon className="w-5 h-5" />
                      </div>
                      
                      <div className="flex-1">
                        <p className="font-medium">{item.label}</p>
                        <p className="text-sm text-gray-500">{item.description}</p>
                      </div>
                      
                      <ChevronRight className="w-5 h-5 text-gray-400" />
                    </button>
                  );
                })}
              </div>
              
              {/* User Section */}
              {user && (
                <div className="mt-8 pt-6 border-t border-gray-200">
                  <div className="flex items-center space-x-3 p-4 bg-blue-50 rounded-xl">
                    <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                      <User className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">
                        {user.displayName || 'User'}
                      </p>
                      <p className="text-sm text-gray-600">
                        {user.email}
                      </p>
                    </div>
                  </div>
                </div>
              )}
              
              {/* App Info */}
              <div className="mt-8 pt-6 border-t border-gray-200">
                <p className="text-xs text-gray-500 text-center">
                  BEC Compare v1.0.0
                </p>
                <p className="text-xs text-gray-400 text-center mt-1">
                  Made with ❤️ for students
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Safe area spacer for bottom navigation */}
      <div className="h-20 md:h-0"></div>
    </>
  );
};

export default TouchNavigation;
