'use client';

import { useState, useEffect, useRef } from 'react';
import { Search, Loader, Clock, TrendingUp, X, Sparkles } from 'lucide-react';
import { getSearchSuggestions } from '../lib/searchAnalytics';
import { generateSearchSuggestions } from '../lib/nlpService';
import { useAuth } from '../hooks/useAuth';

export default function SmartSearch({ 
  onSearch, 
  placeholder = "Ask me anything about engineering colleges...",
  initialQuery = '',
  showSuggestions = true,
  className = ''
}) {
  const [query, setQuery] = useState(initialQuery);
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestionsList, setShowSuggestionsList] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [searchIntent, setSearchIntent] = useState('general');
  const [recentSearches, setRecentSearches] = useState([]);
  
  const { user } = useAuth();
  const searchRef = useRef(null);
  const suggestionsRef = useRef(null);
  const debounceRef = useRef(null);

  // Load suggestions when component mounts
  useEffect(() => {
    if (showSuggestions) {
      loadInitialSuggestions();
    }
  }, [showSuggestions, user]);

  // Handle query changes with debouncing
  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    if (query.length >= 2) {
      debounceRef.current = setTimeout(() => {
        loadSuggestions(query);
      }, 300);
    } else if (query.length === 0) {
      loadInitialSuggestions();
    }

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [query]);

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target) &&
          searchRef.current && !searchRef.current.contains(event.target)) {
        setShowSuggestionsList(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const loadInitialSuggestions = async () => {
    try {
      const [popularSuggestions, nlpSuggestions] = await Promise.all([
        getSearchSuggestions('', user?.uid),
        Promise.resolve(generateSearchSuggestions('engineering colleges'))
      ]);

      const combined = [
        ...popularSuggestions.slice(0, 4),
        ...nlpSuggestions.slice(0, 4)
      ];

      setSuggestions(combined);
    } catch (error) {
      console.error('Error loading initial suggestions:', error);
      setSuggestions(generateSearchSuggestions('engineering colleges'));
    }
  };

  const loadSuggestions = async (searchQuery) => {
    try {
      setIsLoading(true);
      
      const [analyticsSuggestions, nlpSuggestions] = await Promise.all([
        getSearchSuggestions(searchQuery, user?.uid),
        Promise.resolve(generateSearchSuggestions(searchQuery))
      ]);

      const combined = [
        ...analyticsSuggestions.slice(0, 5),
        ...nlpSuggestions.slice(0, 3)
      ];

      setSuggestions(combined);
      setShowSuggestionsList(true);
    } catch (error) {
      console.error('Error loading suggestions:', error);
      setSuggestions(generateSearchSuggestions(searchQuery));
      setShowSuggestionsList(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (query.trim()) {
      performSearch(query.trim());
    }
  };

  const handleSuggestionClick = (suggestion) => {
    const searchText = typeof suggestion === 'string' ? suggestion : suggestion.text;
    setQuery(searchText);
    setShowSuggestionsList(false);
    performSearch(searchText);
  };

  const performSearch = (searchQuery) => {
    // Add to recent searches
    setRecentSearches(prev => {
      const updated = [searchQuery, ...prev.filter(s => s !== searchQuery)].slice(0, 5);
      return updated;
    });

    // Trigger search callback
    if (onSearch) {
      onSearch(searchQuery, {
        userId: user?.uid,
        sessionId: `session_${Date.now()}`
      });
    }
  };

  const clearQuery = () => {
    setQuery('');
    setShowSuggestionsList(false);
    searchRef.current?.focus();
  };

  const getSuggestionIcon = (suggestion) => {
    if (typeof suggestion === 'string') return <Search className="h-4 w-4" />;
    
    switch (suggestion.type) {
      case 'recent':
        return <Clock className="h-4 w-4 text-gray-400" />;
      case 'popular':
        return <TrendingUp className="h-4 w-4 text-blue-500" />;
      case 'template':
        return <Sparkles className="h-4 w-4 text-purple-500" />;
      default:
        return <Search className="h-4 w-4" />;
    }
  };

  const getSuggestionLabel = (suggestion) => {
    if (typeof suggestion === 'string') return null;
    
    switch (suggestion.type) {
      case 'recent':
        return <span className="text-xs text-gray-400 ml-2">Recent</span>;
      case 'popular':
        return <span className="text-xs text-blue-500 ml-2">Popular</span>;
      case 'template':
        return <span className="text-xs text-purple-500 ml-2">Suggested</span>;
      default:
        return null;
    }
  };

  return (
    <div className={`relative ${className}`}>
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
          <input
            ref={searchRef}
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onFocus={() => setShowSuggestionsList(suggestions.length > 0)}
            placeholder={placeholder}
            className="w-full pl-12 pr-12 py-4 text-lg border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm hover:shadow-md"
          />
          
          {query && (
            <button
              type="button"
              onClick={clearQuery}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          )}
          
          {isLoading && (
            <div className="absolute right-12 top-1/2 transform -translate-y-1/2">
              <Loader className="h-5 w-5 animate-spin text-primary-500" />
            </div>
          )}
        </div>
      </form>

      {/* Suggestions Dropdown */}
      {showSuggestionsList && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-lg z-50 max-h-96 overflow-y-auto"
        >
          <div className="py-2">
            {suggestions.map((suggestion, index) => {
              const text = typeof suggestion === 'string' ? suggestion : suggestion.text;
              const intent = typeof suggestion === 'object' ? suggestion.intent : null;
              
              return (
                <button
                  key={index}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center justify-between group"
                >
                  <div className="flex items-center flex-1 min-w-0">
                    {getSuggestionIcon(suggestion)}
                    <span className="ml-3 text-gray-900 truncate">{text}</span>
                    {intent && (
                      <span className="ml-2 px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                        {intent}
                      </span>
                    )}
                  </div>
                  {getSuggestionLabel(suggestion)}
                </button>
              );
            })}
          </div>
          
          {query.length >= 2 && (
            <div className="border-t border-gray-100 px-4 py-2 text-xs text-gray-500">
              <div className="flex items-center">
                <Sparkles className="h-3 w-3 mr-1" />
                Smart search powered by AI
              </div>
            </div>
          )}
        </div>
      )}

      {/* Search Tips */}
      {!query && showSuggestions && (
        <div className="mt-4 text-sm text-gray-600">
          <p className="mb-2 font-medium">Try searching for:</p>
          <div className="flex flex-wrap gap-2">
            {[
              'engineering colleges with good placements',
              'top ranked colleges near me',
              'computer science colleges',
              'colleges with metro access'
            ].map((tip, index) => (
              <button
                key={index}
                onClick={() => handleSuggestionClick(tip)}
                className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-sm transition-colors"
              >
                {tip}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
