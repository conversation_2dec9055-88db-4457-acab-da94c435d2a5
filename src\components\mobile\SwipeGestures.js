'use client';

import { useState, useEffect, useRef } from 'react';
import { ChevronLeft, ChevronRight, RotateCcw } from 'lucide-react';

const SwipeGestures = ({ 
  children, 
  onSwipeLeft, 
  onSwipeRight, 
  onSwipeUp, 
  onSwipeDown,
  threshold = 50,
  restraint = 100,
  allowedTime = 300,
  className = '',
  showIndicators = true,
  disabled = false
}) => {
  const [touchStart, setTouchStart] = useState({ x: 0, y: 0, time: 0 });
  const [touchEnd, setTouchEnd] = useState({ x: 0, y: 0 });
  const [isSwipeActive, setIsSwipeActive] = useState(false);
  const [swipeDirection, setSwipeDirection] = useState(null);
  const [swipeProgress, setSwipeProgress] = useState(0);
  const containerRef = useRef(null);

  // Handle touch start
  const handleTouchStart = (e) => {
    if (disabled) return;
    
    const touch = e.touches[0];
    setTouchStart({
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    });
    setTouchEnd({ x: touch.clientX, y: touch.clientY });
    setIsSwipeActive(true);
    setSwipeDirection(null);
    setSwipeProgress(0);
  };

  // Handle touch move
  const handleTouchMove = (e) => {
    if (disabled || !isSwipeActive) return;
    
    const touch = e.touches[0];
    const deltaX = touch.clientX - touchStart.x;
    const deltaY = touch.clientY - touchStart.y;
    
    setTouchEnd({ x: touch.clientX, y: touch.clientY });
    
    // Determine swipe direction and progress
    const absDeltaX = Math.abs(deltaX);
    const absDeltaY = Math.abs(deltaY);
    
    if (absDeltaX > absDeltaY) {
      // Horizontal swipe
      const direction = deltaX > 0 ? 'right' : 'left';
      setSwipeDirection(direction);
      setSwipeProgress(Math.min(absDeltaX / threshold, 1));
      
      // Prevent vertical scrolling during horizontal swipe
      if (absDeltaX > 10) {
        e.preventDefault();
      }
    } else {
      // Vertical swipe
      const direction = deltaY > 0 ? 'down' : 'up';
      setSwipeDirection(direction);
      setSwipeProgress(Math.min(absDeltaY / threshold, 1));
    }
  };

  // Handle touch end
  const handleTouchEnd = (e) => {
    if (disabled || !isSwipeActive) return;
    
    const deltaX = touchEnd.x - touchStart.x;
    const deltaY = touchEnd.y - touchStart.y;
    const deltaTime = Date.now() - touchStart.time;
    
    const absDeltaX = Math.abs(deltaX);
    const absDeltaY = Math.abs(deltaY);
    
    // Check if swipe meets criteria
    if (deltaTime <= allowedTime) {
      if (absDeltaX >= threshold && absDeltaY <= restraint) {
        // Horizontal swipe
        if (deltaX > 0) {
          onSwipeRight && onSwipeRight(e);
          triggerHapticFeedback();
        } else {
          onSwipeLeft && onSwipeLeft(e);
          triggerHapticFeedback();
        }
      } else if (absDeltaY >= threshold && absDeltaX <= restraint) {
        // Vertical swipe
        if (deltaY > 0) {
          onSwipeDown && onSwipeDown(e);
          triggerHapticFeedback();
        } else {
          onSwipeUp && onSwipeUp(e);
          triggerHapticFeedback();
        }
      }
    }
    
    // Reset state
    setIsSwipeActive(false);
    setSwipeDirection(null);
    setSwipeProgress(0);
  };

  // Trigger haptic feedback
  const triggerHapticFeedback = () => {
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
  };

  // Handle mouse events for desktop testing
  const handleMouseDown = (e) => {
    if (disabled) return;
    
    setTouchStart({
      x: e.clientX,
      y: e.clientY,
      time: Date.now()
    });
    setTouchEnd({ x: e.clientX, y: e.clientY });
    setIsSwipeActive(true);
  };

  const handleMouseMove = (e) => {
    if (disabled || !isSwipeActive) return;
    
    const deltaX = e.clientX - touchStart.x;
    const deltaY = e.clientY - touchStart.y;
    
    setTouchEnd({ x: e.clientX, y: e.clientY });
    
    const absDeltaX = Math.abs(deltaX);
    const absDeltaY = Math.abs(deltaY);
    
    if (absDeltaX > absDeltaY) {
      const direction = deltaX > 0 ? 'right' : 'left';
      setSwipeDirection(direction);
      setSwipeProgress(Math.min(absDeltaX / threshold, 1));
    } else {
      const direction = deltaY > 0 ? 'down' : 'up';
      setSwipeDirection(direction);
      setSwipeProgress(Math.min(absDeltaY / threshold, 1));
    }
  };

  const handleMouseUp = (e) => {
    if (disabled || !isSwipeActive) return;
    
    const deltaX = touchEnd.x - touchStart.x;
    const deltaY = touchEnd.y - touchStart.y;
    const deltaTime = Date.now() - touchStart.time;
    
    const absDeltaX = Math.abs(deltaX);
    const absDeltaY = Math.abs(deltaY);
    
    if (deltaTime <= allowedTime) {
      if (absDeltaX >= threshold && absDeltaY <= restraint) {
        if (deltaX > 0) {
          onSwipeRight && onSwipeRight(e);
        } else {
          onSwipeLeft && onSwipeLeft(e);
        }
      } else if (absDeltaY >= threshold && absDeltaX <= restraint) {
        if (deltaY > 0) {
          onSwipeDown && onSwipeDown(e);
        } else {
          onSwipeUp && onSwipeUp(e);
        }
      }
    }
    
    setIsSwipeActive(false);
    setSwipeDirection(null);
    setSwipeProgress(0);
  };

  // Get swipe indicator style
  const getSwipeIndicatorStyle = () => {
    if (!isSwipeActive || !swipeDirection) return {};
    
    const opacity = Math.min(swipeProgress * 2, 1);
    const scale = 0.8 + (swipeProgress * 0.2);
    
    return {
      opacity,
      transform: `scale(${scale})`,
      transition: 'none'
    };
  };

  // Get swipe indicator position
  const getSwipeIndicatorPosition = () => {
    switch (swipeDirection) {
      case 'left':
        return 'left-4 top-1/2 -translate-y-1/2';
      case 'right':
        return 'right-4 top-1/2 -translate-y-1/2';
      case 'up':
        return 'top-4 left-1/2 -translate-x-1/2';
      case 'down':
        return 'bottom-4 left-1/2 -translate-x-1/2';
      default:
        return 'hidden';
    }
  };

  // Get swipe indicator icon
  const getSwipeIndicatorIcon = () => {
    switch (swipeDirection) {
      case 'left':
        return <ChevronLeft className="w-8 h-8" />;
      case 'right':
        return <ChevronRight className="w-8 h-8" />;
      case 'up':
      case 'down':
        return <RotateCcw className="w-8 h-8" />;
      default:
        return null;
    }
  };

  return (
    <div
      ref={containerRef}
      className={`relative select-none ${className}`}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
      style={{ touchAction: disabled ? 'auto' : 'pan-y' }}
    >
      {children}
      
      {/* Swipe Indicators */}
      {showIndicators && isSwipeActive && swipeDirection && (
        <div
          className={`absolute z-10 pointer-events-none ${getSwipeIndicatorPosition()}`}
          style={getSwipeIndicatorStyle()}
        >
          <div className="bg-blue-600 text-white rounded-full p-3 shadow-lg">
            {getSwipeIndicatorIcon()}
          </div>
        </div>
      )}
      
      {/* Swipe Progress Indicator */}
      {showIndicators && isSwipeActive && swipeProgress > 0 && (
        <div className="absolute inset-0 pointer-events-none">
          <div 
            className={`absolute bg-blue-600 bg-opacity-10 transition-all duration-100 ${
              swipeDirection === 'left' || swipeDirection === 'right' 
                ? 'top-0 bottom-0' 
                : 'left-0 right-0'
            }`}
            style={{
              ...(swipeDirection === 'left' && {
                right: 0,
                width: `${swipeProgress * 100}%`
              }),
              ...(swipeDirection === 'right' && {
                left: 0,
                width: `${swipeProgress * 100}%`
              }),
              ...(swipeDirection === 'up' && {
                bottom: 0,
                height: `${swipeProgress * 100}%`
              }),
              ...(swipeDirection === 'down' && {
                top: 0,
                height: `${swipeProgress * 100}%`
              })
            }}
          />
        </div>
      )}
    </div>
  );
};

export default SwipeGestures;
