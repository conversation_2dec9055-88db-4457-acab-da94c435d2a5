// Mock Analytics Service for Testing
// This replaces Firebase analytics when Firebase is not configured

let mockData = {
  searchQueries: [],
  searchInteractions: [],
  searchPerformance: [],
  popularSearches: [
    { query: 'engineering colleges bangalore', intent: 'general', timestamp: new Date() },
    { query: 'top ranked colleges', intent: 'ranking', timestamp: new Date() },
    { query: 'colleges near electronic city', intent: 'location', timestamp: new Date() },
    { query: 'computer science engineering', intent: 'courses', timestamp: new Date() },
    { query: 'colleges with good placements', intent: 'placement', timestamp: new Date() }
  ]
};

// Mock search suggestions
const mockSuggestions = [
  { text: 'engineering colleges with good placements', type: 'template', intent: 'placement' },
  { text: 'top ranked colleges in Bangalore', type: 'template', intent: 'ranking' },
  { text: 'colleges near Electronic City', type: 'template', intent: 'location' },
  { text: 'computer science engineering colleges', type: 'template', intent: 'courses' },
  { text: 'colleges with metro connectivity', type: 'template', intent: 'infrastructure' },
  { text: 'affordable engineering colleges', type: 'template', intent: 'fees' }
];

export const trackSearchQuery = async (searchData) => {
  console.log('🔍 Mock Analytics - Search Query Tracked:', {
    query: searchData.query,
    intent: searchData.intent,
    resultsCount: searchData.resultsCount,
    timestamp: new Date().toISOString()
  });
  
  mockData.searchQueries.push({
    ...searchData,
    timestamp: new Date(),
    id: Date.now()
  });
  
  return true;
};

export const trackSearchInteraction = async (interactionData) => {
  console.log('👆 Mock Analytics - Search Interaction Tracked:', {
    query: interactionData.query,
    collegeId: interactionData.collegeId,
    action: interactionData.action,
    timestamp: new Date().toISOString()
  });
  
  mockData.searchInteractions.push({
    ...interactionData,
    timestamp: new Date(),
    id: Date.now()
  });
  
  return true;
};

export const trackSearchPerformance = async (performanceData) => {
  console.log('⚡ Mock Analytics - Search Performance Tracked:', {
    query: performanceData.query,
    searchTime: performanceData.searchTime + 'ms',
    resultsCount: performanceData.resultsCount,
    timestamp: new Date().toISOString()
  });
  
  mockData.searchPerformance.push({
    ...performanceData,
    timestamp: new Date(),
    id: Date.now()
  });
  
  return true;
};

export const getPopularSearches = async (limitCount = 10) => {
  console.log('📊 Mock Analytics - Getting Popular Searches');
  return mockData.popularSearches.slice(0, limitCount);
};

export const getSearchSuggestions = async (partialQuery, userId = null) => {
  console.log('💡 Mock Analytics - Getting Search Suggestions for:', partialQuery);
  
  if (!partialQuery || partialQuery.length < 2) {
    return mockSuggestions.slice(0, 5);
  }
  
  // Filter suggestions based on partial query
  const filtered = mockSuggestions.filter(suggestion =>
    suggestion.text.toLowerCase().includes(partialQuery.toLowerCase())
  );
  
  // Add some recent searches if user exists
  if (userId) {
    const recentSearches = mockData.searchQueries
      .filter(q => q.userId === userId)
      .slice(-3)
      .map(q => ({
        text: q.query,
        type: 'recent',
        intent: q.intent
      }));
    
    return [...recentSearches, ...filtered].slice(0, 8);
  }
  
  return filtered.slice(0, 5);
};

export const getSearchAnalytics = async (timeRange = '7d') => {
  console.log('📈 Mock Analytics - Getting Search Analytics for:', timeRange);
  
  return {
    totalSearches: mockData.searchQueries.length,
    uniqueUsers: new Set(mockData.searchQueries.map(q => q.userId)).size,
    intentDistribution: {
      general: 15,
      location: 12,
      placement: 18,
      ranking: 10,
      courses: 14,
      infrastructure: 8,
      fees: 6
    },
    topQueries: {
      'engineering colleges bangalore': 25,
      'top ranked colleges': 18,
      'colleges near electronic city': 15,
      'computer science engineering': 12,
      'good placement colleges': 10
    },
    avgResultsPerQuery: 12.5,
    searchTrends: []
  };
};

// Get mock data for debugging
export const getMockData = () => {
  return {
    ...mockData,
    totalQueries: mockData.searchQueries.length,
    totalInteractions: mockData.searchInteractions.length,
    totalPerformanceRecords: mockData.searchPerformance.length
  };
};

// Clear mock data
export const clearMockData = () => {
  mockData = {
    searchQueries: [],
    searchInteractions: [],
    searchPerformance: [],
    popularSearches: mockData.popularSearches // Keep popular searches
  };
  console.log('🧹 Mock Analytics - Data Cleared');
};

export default {
  trackSearchQuery,
  trackSearchInteraction,
  trackSearchPerformance,
  getPopularSearches,
  getSearchSuggestions,
  getSearchAnalytics,
  getMockData,
  clearMockData
};
