'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  Heart, 
  FileText, 
  Search, 
  TrendingUp, 
  Clock, 
  ArrowRight,
  Star,
  MapPin,
  Users,
  Award,
  BookOpen
} from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { useFavorites } from '../../hooks/useFavorites';
import { getUserStats, getUserComparisonHistory, getUserSearchHistory } from '../../lib/userService';
import { getCollegeById } from '../../lib/collegeData';
import CollegeCard from '../../components/CollegeCard';
import RecommendationsSection from '../../components/dashboard/RecommendationsSection';

const DashboardPage = () => {
  const { user } = useAuth();
  const { favorites, loading: favoritesLoading } = useFavorites();
  const [stats, setStats] = useState({
    totalFavorites: 0,
    totalApplications: 0,
    totalSearches: 0,
    totalComparisons: 0,
    recentActivity: {}
  });
  const [recentComparisons, setRecentComparisons] = useState([]);
  const [recentSearches, setRecentSearches] = useState([]);
  const [favoriteColleges, setFavoriteColleges] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadDashboardData = async () => {
      if (!user) return;

      try {
        setLoading(true);
        
        // Load user stats
        const userStats = await getUserStats(user.uid);
        setStats(userStats);

        // Load recent activity
        const [comparisons, searches] = await Promise.all([
          getUserComparisonHistory(user.uid, 5),
          getUserSearchHistory(user.uid, 5)
        ]);
        
        setRecentComparisons(comparisons);
        setRecentSearches(searches);

      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, [user]);

  // Load college details for favorites
  useEffect(() => {
    const loadFavoriteColleges = async () => {
      if (favorites.length === 0) {
        setFavoriteColleges([]);
        return;
      }

      try {
        const collegePromises = favorites.slice(0, 6).map(async (favorite) => {
          const college = await getCollegeById(favorite.collegeId);
          return college;
        });

        const colleges = await Promise.all(collegePromises);
        setFavoriteColleges(colleges.filter(Boolean));
      } catch (error) {
        console.error('Error loading favorite colleges:', error);
      }
    };

    loadFavoriteColleges();
  }, [favorites]);

  const statCards = [
    {
      title: 'Favorite Colleges',
      value: stats.totalFavorites,
      icon: Heart,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      href: '/dashboard/favorites'
    },
    {
      title: 'Applications',
      value: stats.totalApplications,
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      href: '/dashboard/applications'
    },
    {
      title: 'Searches',
      value: stats.totalSearches,
      icon: Search,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      href: '/colleges'
    },
    {
      title: 'Comparisons',
      value: stats.totalComparisons,
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      href: '/compare'
    }
  ];

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-xl shadow-sm p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-primary-600 to-secondary-600 rounded-2xl p-8 text-white">
        <div className="max-w-3xl">
          <h1 className="text-3xl font-bold mb-2">
            Welcome back, {user?.displayName?.split(' ')[0] || 'Student'}! 👋
          </h1>
          <p className="text-primary-100 text-lg">
            Here's your personalized college exploration dashboard. Track your favorites, manage applications, and discover new opportunities.
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat) => {
          const Icon = stat.icon;
          return (
            <Link
              key={stat.title}
              href={stat.href}
              className="bg-white rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow group"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className={`${stat.bgColor} p-3 rounded-lg group-hover:scale-110 transition-transform`}>
                  <Icon className={`w-6 h-6 ${stat.color}`} />
                </div>
              </div>
            </Link>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Favorites */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2">
              <Heart className="w-5 h-5 text-red-600" />
              <h2 className="text-xl font-semibold text-gray-900">Recent Favorites</h2>
            </div>
            <Link
              href="/dashboard/favorites"
              className="text-primary-600 hover:text-primary-700 text-sm font-medium flex items-center space-x-1"
            >
              <span>View all</span>
              <ArrowRight className="w-4 h-4" />
            </Link>
          </div>

          {favoritesLoading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          ) : favoriteColleges.length > 0 ? (
            <div className="space-y-4">
              {favoriteColleges.slice(0, 3).map((college) => (
                <Link
                  key={college.id}
                  href={`/colleges/${college.id}`}
                  className="flex items-center space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                    <BookOpen className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-gray-900 truncate">{college.name}</p>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <MapPin className="w-3 h-3" />
                        <span>{college.location}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Award className="w-3 h-3" />
                        <span>Rank #{college.ranking}</span>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Heart className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">No favorites yet</p>
              <Link
                href="/colleges"
                className="btn-primary text-sm"
              >
                Explore Colleges
              </Link>
            </div>
          )}
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center space-x-2 mb-6">
            <Clock className="w-5 h-5 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">Recent Activity</h2>
          </div>

          <div className="space-y-4">
            {recentSearches.length > 0 && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Recent Searches</h3>
                <div className="space-y-2">
                  {recentSearches.slice(0, 3).map((search, index) => (
                    <div key={index} className="flex items-center space-x-3 text-sm">
                      <Search className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-600">"{search.query}"</span>
                      <span className="text-gray-400">
                        {new Date(search.timestamp?.toDate()).toLocaleDateString()}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {recentComparisons.length > 0 && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Recent Comparisons</h3>
                <div className="space-y-2">
                  {recentComparisons.slice(0, 3).map((comparison, index) => (
                    <div key={index} className="flex items-center space-x-3 text-sm">
                      <TrendingUp className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-600">
                        Compared {comparison.collegeIds?.length || 0} colleges
                      </span>
                      <span className="text-gray-400">
                        {new Date(comparison.timestamp?.toDate()).toLocaleDateString()}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {recentSearches.length === 0 && recentComparisons.length === 0 && (
              <div className="text-center py-8">
                <Clock className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">No recent activity</p>
                <Link
                  href="/colleges"
                  className="btn-secondary text-sm"
                >
                  Start Exploring
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link
            href="/colleges"
            className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors group"
          >
            <Search className="w-6 h-6 text-primary-600" />
            <div>
              <p className="font-medium text-gray-900 group-hover:text-primary-700">Search Colleges</p>
              <p className="text-sm text-gray-500">Find your perfect match</p>
            </div>
          </Link>

          <Link
            href="/compare"
            className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors group"
          >
            <TrendingUp className="w-6 h-6 text-primary-600" />
            <div>
              <p className="font-medium text-gray-900 group-hover:text-primary-700">Compare Colleges</p>
              <p className="text-sm text-gray-500">Side-by-side comparison</p>
            </div>
          </Link>

          <Link
            href="/dashboard/applications"
            className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors group"
          >
            <FileText className="w-6 h-6 text-primary-600" />
            <div>
              <p className="font-medium text-gray-900 group-hover:text-primary-700">Track Applications</p>
              <p className="text-sm text-gray-500">Manage your progress</p>
            </div>
          </Link>
        </div>
      </div>

      {/* Personalized Recommendations */}
      <RecommendationsSection limit={6} />
    </div>
  );
};

export default DashboardPage;
