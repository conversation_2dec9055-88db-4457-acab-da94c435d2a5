// Basic tests for analytics data functionality
// Note: This is a simple test file for demonstration purposes

import { generateCollegeAnalytics, calculateROI, getAnalyticsOverview } from '../analyticsData';

// Mock college data for testing
const mockCollege = {
  id: 1,
  name: "Test Engineering College",
  acronym: "TEC",
  ranking: 5,
  placementRate: 85,
  highestPackage: 45,
  nirf: "101-150",
  fees: 400000
};

const mockColleges = [mockCollege];

// Test analytics data generation
export const testAnalyticsGeneration = () => {
  console.log('Testing analytics data generation...');
  
  try {
    const analytics = generateCollegeAnalytics(mockCollege);
    
    // Verify analytics structure
    const requiredFields = [
      'id', 'name', 'acronym', 'placementTrends', 
      'salaryDistribution', 'careerPaths', 'admissionDifficulty', 'roiData'
    ];
    
    const missingFields = requiredFields.filter(field => !analytics.hasOwnProperty(field));
    
    if (missingFields.length > 0) {
      console.error('Missing fields in analytics:', missingFields);
      return false;
    }
    
    // Verify placement trends
    if (!Array.isArray(analytics.placementTrends) || analytics.placementTrends.length !== 5) {
      console.error('Invalid placement trends data');
      return false;
    }
    
    // Verify salary distribution
    if (!Array.isArray(analytics.salaryDistribution) || analytics.salaryDistribution.length === 0) {
      console.error('Invalid salary distribution data');
      return false;
    }
    
    // Verify career paths
    if (!Array.isArray(analytics.careerPaths) || analytics.careerPaths.length === 0) {
      console.error('Invalid career paths data');
      return false;
    }
    
    // Verify admission difficulty
    const admissionDifficulty = analytics.admissionDifficulty;
    if (!admissionDifficulty.score || !admissionDifficulty.difficulty || !admissionDifficulty.probability) {
      console.error('Invalid admission difficulty data');
      return false;
    }
    
    console.log('✅ Analytics generation test passed');
    return true;
    
  } catch (error) {
    console.error('Analytics generation test failed:', error);
    return false;
  }
};

// Test ROI calculation
export const testROICalculation = () => {
  console.log('Testing ROI calculation...');
  
  try {
    const roi = calculateROI(400000, 800000, 5);
    
    // Verify ROI structure
    const requiredFields = [
      'totalInvestment', 'totalEarnings', 'netROI', 
      'roiPercentage', 'breakEvenMonths', 'yearlyBreakdown'
    ];
    
    const missingFields = requiredFields.filter(field => !roi.hasOwnProperty(field));
    
    if (missingFields.length > 0) {
      console.error('Missing fields in ROI calculation:', missingFields);
      return false;
    }
    
    // Verify yearly breakdown
    if (!Array.isArray(roi.yearlyBreakdown) || roi.yearlyBreakdown.length !== 5) {
      console.error('Invalid yearly breakdown data');
      return false;
    }
    
    // Verify calculation logic
    if (roi.totalInvestment !== 400000) {
      console.error('Incorrect total investment calculation');
      return false;
    }
    
    console.log('✅ ROI calculation test passed');
    return true;
    
  } catch (error) {
    console.error('ROI calculation test failed:', error);
    return false;
  }
};

// Test analytics overview
export const testAnalyticsOverview = async () => {
  console.log('Testing analytics overview...');
  
  try {
    const overview = await getAnalyticsOverview(mockColleges);
    
    // Verify overview structure
    const requiredFields = [
      'totalColleges', 'averagePlacementRate', 'topPerformers',
      'industryDistribution', 'salaryTrends'
    ];
    
    const missingFields = requiredFields.filter(field => !overview.hasOwnProperty(field));
    
    if (missingFields.length > 0) {
      console.error('Missing fields in analytics overview:', missingFields);
      return false;
    }
    
    // Verify data types
    if (typeof overview.totalColleges !== 'number' || overview.totalColleges !== 1) {
      console.error('Incorrect total colleges count');
      return false;
    }
    
    if (!Array.isArray(overview.topPerformers) || overview.topPerformers.length === 0) {
      console.error('Invalid top performers data');
      return false;
    }
    
    console.log('✅ Analytics overview test passed');
    return true;
    
  } catch (error) {
    console.error('Analytics overview test failed:', error);
    return false;
  }
};

// Run all tests
export const runAllTests = async () => {
  console.log('🧪 Running Analytics Tests...\n');
  
  const tests = [
    testAnalyticsGeneration,
    testROICalculation,
    testAnalyticsOverview
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    const result = await test();
    if (result) passedTests++;
    console.log(''); // Add spacing between tests
  }
  
  console.log(`📊 Test Results: ${passedTests}/${tests.length} tests passed`);
  
  if (passedTests === tests.length) {
    console.log('🎉 All analytics tests passed successfully!');
  } else {
    console.log('❌ Some tests failed. Please check the implementation.');
  }
  
  return passedTests === tests.length;
};

// Export test functions for manual testing
if (typeof window !== 'undefined') {
  window.analyticsTests = {
    runAllTests,
    testAnalyticsGeneration,
    testROICalculation,
    testAnalyticsOverview
  };
}
