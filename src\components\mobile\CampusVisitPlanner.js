'use client';

import { useState, useEffect } from 'react';
import {
  Calendar,
  Clock,
  MapPin,
  Route,
  Plus,
  Trash2,
  Edit3,
  Navigation,
  Car,
  CheckCircle,
  AlertCircle,
  Share2
} from 'lucide-react';
import { enhancedLocationService } from '../../lib/geoService';
import OpenStreetMapIntegration from './OpenStreetMapIntegration';

const CampusVisitPlanner = ({ colleges = [], onPlanUpdate }) => {
  const [visitPlan, setVisitPlan] = useState({
    date: '',
    startTime: '09:00',
    colleges: [],
    notes: ''
  });
  const [availableColleges, setAvailableColleges] = useState(colleges);
  const [selectedCollege, setSelectedCollege] = useState(null);
  const [routeInfo, setRouteInfo] = useState(null);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [showMap, setShowMap] = useState(false);
  const [userLocation, setUserLocation] = useState(null);

  useEffect(() => {
    // Get user location for route optimization
    enhancedLocationService.startWatching((position, error) => {
      if (!error && position) {
        setUserLocation({
          lat: position.coords.latitude,
          lng: position.coords.longitude
        });
      }
    });

    return () => {
      enhancedLocationService.stopWatching();
    };
  }, []);

  useEffect(() => {
    if (visitPlan.colleges.length > 0) {
      calculateRouteInfo();
    }
  }, [visitPlan.colleges, userLocation]);

  // Add college to visit plan
  const addCollegeToVisit = (college) => {
    if (visitPlan.colleges.find(c => c.id === college.id)) return;

    const newCollege = {
      ...college,
      visitTime: '10:00',
      duration: 60, // minutes
      priority: 'medium',
      notes: ''
    };

    setVisitPlan(prev => ({
      ...prev,
      colleges: [...prev.colleges, newCollege]
    }));

    setAvailableColleges(prev => prev.filter(c => c.id !== college.id));
  };

  // Remove college from visit plan
  const removeCollegeFromVisit = (collegeId) => {
    const college = visitPlan.colleges.find(c => c.id === collegeId);
    if (!college) return;

    setVisitPlan(prev => ({
      ...prev,
      colleges: prev.colleges.filter(c => c.id !== collegeId)
    }));

    setAvailableColleges(prev => [...prev, college]);
  };

  // Update college visit details
  const updateCollegeVisit = (collegeId, updates) => {
    setVisitPlan(prev => ({
      ...prev,
      colleges: prev.colleges.map(college =>
        college.id === collegeId ? { ...college, ...updates } : college
      )
    }));
  };

  // Optimize route based on location
  const optimizeRoute = async () => {
    if (!userLocation || visitPlan.colleges.length < 2) return;

    setIsOptimizing(true);

    try {
      // Calculate distances from user location to each college
      const collegesWithDistance = visitPlan.colleges.map(college => ({
        ...college,
        distanceFromUser: calculateDistance(userLocation, {
          lat: college.latitude,
          lng: college.longitude
        })
      }));

      // Simple optimization: sort by distance from user
      const optimizedColleges = collegesWithDistance.sort((a, b) => 
        a.distanceFromUser - b.distanceFromUser
      );

      // Update visit times based on travel time
      let currentTime = new Date(`2024-01-01 ${visitPlan.startTime}`);
      const updatedColleges = optimizedColleges.map((college, index) => {
        const visitTime = currentTime.toTimeString().slice(0, 5);
        
        // Add visit duration and travel time to next college
        currentTime.setMinutes(currentTime.getMinutes() + college.duration);
        if (index < optimizedColleges.length - 1) {
          currentTime.setMinutes(currentTime.getMinutes() + 30); // 30 min travel time
        }

        return {
          ...college,
          visitTime,
          optimized: true
        };
      });

      setVisitPlan(prev => ({
        ...prev,
        colleges: updatedColleges
      }));

    } catch (error) {
      console.error('Error optimizing route:', error);
    } finally {
      setIsOptimizing(false);
    }
  };

  // Calculate route information
  const calculateRouteInfo = async () => {
    if (!userLocation || visitPlan.colleges.length === 0) return;

    try {
      const route = await enhancedLocationService.planCampusVisitRoute(visitPlan.colleges);
      setRouteInfo(route);
    } catch (error) {
      console.error('Error calculating route info:', error);
    }
  };

  // Calculate distance between two points
  const calculateDistance = (point1, point2) => {
    const R = 6371; // Earth's radius in km
    const dLat = (point2.lat - point1.lat) * Math.PI / 180;
    const dLon = (point2.lng - point1.lng) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(point1.lat * Math.PI / 180) * Math.cos(point2.lat * Math.PI / 180) *
              Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  // Share visit plan
  const shareVisitPlan = async () => {
    const planText = `Campus Visit Plan - ${visitPlan.date}\n\n` +
      visitPlan.colleges.map((college, index) => 
        `${index + 1}. ${college.name}\n   Time: ${college.visitTime}\n   Duration: ${college.duration} min\n`
      ).join('\n') +
      `\nTotal Duration: ${routeInfo?.estimatedDuration.formatted || 'Calculating...'}\n` +
      `Total Distance: ${routeInfo?.totalDistance.toFixed(1) || 'Calculating...'} km`;

    try {
      if (navigator.share) {
        await navigator.share({
          title: 'Campus Visit Plan',
          text: planText
        });
      } else {
        await navigator.clipboard.writeText(planText);
        showToast('Plan copied to clipboard!');
      }
    } catch (error) {
      console.error('Error sharing plan:', error);
    }
  };

  // Show toast message
  const showToast = (message) => {
    const toast = document.createElement('div');
    toast.className = 'fixed bottom-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-2 rounded-lg z-50';
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => {
      if (toast.parentElement) {
        toast.remove();
      }
    }, 2000);
  };

  // Get priority color
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-900">Campus Visit Planner</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowMap(!showMap)}
              className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-lg transition-colors duration-200"
            >
              <MapPin className="w-5 h-5" />
            </button>
            
            {visitPlan.colleges.length > 0 && (
              <button
                onClick={shareVisitPlan}
                className="bg-gray-600 hover:bg-gray-700 text-white p-2 rounded-lg transition-colors duration-200"
              >
                <Share2 className="w-5 h-5" />
              </button>
            )}
          </div>
        </div>

        {/* Plan Details */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Visit Date
            </label>
            <input
              type="date"
              value={visitPlan.date}
              onChange={(e) => setVisitPlan(prev => ({ ...prev, date: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Start Time
            </label>
            <input
              type="time"
              value={visitPlan.startTime}
              onChange={(e) => setVisitPlan(prev => ({ ...prev, startTime: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div className="flex items-end">
            <button
              onClick={optimizeRoute}
              disabled={isOptimizing || visitPlan.colleges.length < 2}
              className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
            >
              {isOptimizing ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Route className="w-4 h-4" />
              )}
              <span>{isOptimizing ? 'Optimizing...' : 'Optimize Route'}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Map View */}
      {showMap && (
        <div className="p-6 border-b border-gray-200">
          <OpenStreetMapIntegration
            colleges={visitPlan.colleges}
            selectedCollege={selectedCollege}
            onCollegeSelect={setSelectedCollege}
            showRoute={true}
            className="h-64"
          />
        </div>
      )}

      {/* Route Summary */}
      {routeInfo && (
        <div className="p-6 border-b border-gray-200 bg-blue-50">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{visitPlan.colleges.length}</div>
              <div className="text-sm text-gray-600">Colleges</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{routeInfo.totalDistance.toFixed(1)} km</div>
              <div className="text-sm text-gray-600">Total Distance</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{routeInfo.estimatedDuration.formatted}</div>
              <div className="text-sm text-gray-600">Estimated Time</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {visitPlan.date ? new Date(visitPlan.date).toLocaleDateString('en-US', { weekday: 'short' }) : '-'}
              </div>
              <div className="text-sm text-gray-600">Day</div>
            </div>
          </div>
        </div>
      )}

      {/* Visit Schedule */}
      <div className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Visit Schedule</h3>
        
        {visitPlan.colleges.length === 0 ? (
          <div className="text-center py-8">
            <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">No colleges planned</h4>
            <p className="text-gray-600 mb-4">Add colleges to your visit plan to get started</p>
          </div>
        ) : (
          <div className="space-y-4">
            {visitPlan.colleges.map((college, index) => (
              <div key={college.id} className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <span className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium">
                        {index + 1}
                      </span>
                      <h4 className="font-semibold text-gray-900">{college.name}</h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(college.priority)}`}>
                        {college.priority}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
                      <div>
                        <label className="block text-xs text-gray-600 mb-1">Visit Time</label>
                        <input
                          type="time"
                          value={college.visitTime}
                          onChange={(e) => updateCollegeVisit(college.id, { visitTime: e.target.value })}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-xs text-gray-600 mb-1">Duration (min)</label>
                        <input
                          type="number"
                          value={college.duration}
                          onChange={(e) => updateCollegeVisit(college.id, { duration: parseInt(e.target.value) })}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                          min="30"
                          max="240"
                          step="15"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-xs text-gray-600 mb-1">Priority</label>
                        <select
                          value={college.priority}
                          onChange={(e) => updateCollegeVisit(college.id, { priority: e.target.value })}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                        >
                          <option value="low">Low</option>
                          <option value="medium">Medium</option>
                          <option value="high">High</option>
                        </select>
                      </div>
                    </div>
                    
                    <textarea
                      placeholder="Notes for this visit..."
                      value={college.notes}
                      onChange={(e) => updateCollegeVisit(college.id, { notes: e.target.value })}
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-1 focus:ring-blue-500 resize-none"
                      rows="2"
                    />
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => {
                        const url = `https://www.openstreetmap.org/directions?from=${userLocation?.lat || ''},${userLocation?.lng || ''}&to=${college.latitude},${college.longitude}&route=driving`;
                        window.open(url, '_blank');
                      }}
                      className="text-blue-600 hover:text-blue-700 p-1"
                      title="Get directions"
                    >
                      <Navigation className="w-4 h-4" />
                    </button>
                    
                    <button
                      onClick={() => removeCollegeFromVisit(college.id)}
                      className="text-red-600 hover:text-red-700 p-1"
                      title="Remove from plan"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Available Colleges */}
        {availableColleges.length > 0 && (
          <div className="mt-6">
            <h4 className="text-md font-medium text-gray-900 mb-3">Add More Colleges</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {availableColleges.slice(0, 6).map(college => (
                <button
                  key={college.id}
                  onClick={() => addCollegeToVisit(college)}
                  className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200 text-left"
                >
                  <div>
                    <p className="font-medium text-gray-900">{college.name}</p>
                    <p className="text-sm text-gray-600">Rank #{college.rank}</p>
                  </div>
                  <Plus className="w-5 h-5 text-blue-600" />
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CampusVisitPlanner;
