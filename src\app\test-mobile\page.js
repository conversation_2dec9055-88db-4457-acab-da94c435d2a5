'use client';

import { useState, useEffect } from 'react';
import { 
  Smartphone, 
  Wifi, 
  Bell, 
  Camera, 
  MapPin, 
  CheckCircle, 
  XCircle,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';
import PushNotifications from '../../components/PushNotifications';
import CameraScanner from '../../components/mobile/CameraScanner';
import DocumentManager from '../../components/mobile/DocumentManager';
import OpenStreetMapIntegration from '../../components/mobile/OpenStreetMapIntegration';
import SwipeGestures from '../../components/mobile/SwipeGestures';
import PullToRefresh from '../../components/mobile/PullToRefresh';
import TouchNavigation from '../../components/mobile/TouchNavigation';
import PWAInstallPrompt from '../../components/PWAInstallPrompt';

export default function TestMobilePage() {
  const [testResults, setTestResults] = useState({});
  const [currentTest, setCurrentTest] = useState('');
  const [showCamera, setShowCamera] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Mock college data for testing
  const mockColleges = [
    {
      id: 'rvce',
      name: 'R.V. College of Engineering',
      latitude: 12.9237,
      longitude: 77.4987,
      rank: 1,
      placementRate: 95
    },
    {
      id: 'msrit',
      name: 'M.S. Ramaiah Institute of Technology',
      latitude: 13.0207,
      longitude: 77.5651,
      rank: 2,
      placementRate: 92
    },
    {
      id: 'pes',
      name: 'PES University',
      latitude: 12.9342,
      longitude: 77.6101,
      rank: 3,
      placementRate: 90
    }
  ];

  useEffect(() => {
    runAutomaticTests();
  }, []);

  const runAutomaticTests = async () => {
    const results = {};

    // Test 1: Service Worker Registration
    setCurrentTest('Service Worker Registration');
    try {
      if ('serviceWorker' in navigator) {
        const registration = await navigator.serviceWorker.getRegistration();
        results.serviceWorker = registration ? 'PASS' : 'FAIL';
      } else {
        results.serviceWorker = 'NOT_SUPPORTED';
      }
    } catch (error) {
      results.serviceWorker = 'ERROR';
    }

    // Test 2: PWA Manifest
    setCurrentTest('PWA Manifest');
    try {
      const response = await fetch('/manifest.json');
      results.manifest = response.ok ? 'PASS' : 'FAIL';
    } catch (error) {
      results.manifest = 'ERROR';
    }

    // Test 3: Local Storage (Mock Firebase)
    setCurrentTest('Mock Firebase Services');
    try {
      localStorage.setItem('test-key', 'test-value');
      const value = localStorage.getItem('test-key');
      localStorage.removeItem('test-key');
      results.mockFirebase = value === 'test-value' ? 'PASS' : 'FAIL';
    } catch (error) {
      results.mockFirebase = 'ERROR';
    }

    // Test 4: Notification API
    setCurrentTest('Notification API');
    try {
      results.notifications = 'Notification' in window ? 'PASS' : 'NOT_SUPPORTED';
    } catch (error) {
      results.notifications = 'ERROR';
    }

    // Test 5: Camera API
    setCurrentTest('Camera API');
    try {
      results.camera = (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) ? 'PASS' : 'NOT_SUPPORTED';
    } catch (error) {
      results.camera = 'ERROR';
    }

    // Test 6: Geolocation API
    setCurrentTest('Geolocation API');
    try {
      results.geolocation = 'geolocation' in navigator ? 'PASS' : 'NOT_SUPPORTED';
    } catch (error) {
      results.geolocation = 'ERROR';
    }

    // Test 7: Touch Events
    setCurrentTest('Touch Events');
    try {
      results.touchEvents = 'ontouchstart' in window ? 'PASS' : 'NOT_SUPPORTED';
    } catch (error) {
      results.touchEvents = 'ERROR';
    }

    // Test 8: Offline Detection
    setCurrentTest('Offline Detection');
    try {
      results.offlineDetection = 'onLine' in navigator ? 'PASS' : 'NOT_SUPPORTED';
    } catch (error) {
      results.offlineDetection = 'ERROR';
    }

    setTestResults(results);
    setCurrentTest('');
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate refresh
    await runAutomaticTests();
    setIsRefreshing(false);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'PASS':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'FAIL':
      case 'ERROR':
        return <XCircle className="w-5 h-5 text-red-600" />;
      case 'NOT_SUPPORTED':
        return <AlertTriangle className="w-5 h-5 text-yellow-600" />;
      default:
        return <div className="w-5 h-5 bg-gray-300 rounded-full animate-pulse" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'PASS':
        return 'text-green-600 bg-green-100';
      case 'FAIL':
      case 'ERROR':
        return 'text-red-600 bg-red-100';
      case 'NOT_SUPPORTED':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Smartphone className="w-8 h-8 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">Mobile App Features Test</h1>
          </div>
          
          <p className="text-gray-600 mb-4">
            Comprehensive testing of all mobile app features including PWA, notifications, camera, maps, and gestures.
          </p>

          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
          >
            <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            <span>{isRefreshing ? 'Running Tests...' : 'Re-run Tests'}</span>
          </button>
        </div>

        {/* Test Results */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Automated Test Results</h2>
          
          {currentTest && (
            <div className="mb-4 p-3 bg-blue-50 rounded-lg">
              <p className="text-blue-800">Currently testing: {currentTest}</p>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(testResults).map(([test, status]) => (
              <div key={test} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                <span className="font-medium text-gray-900 capitalize">
                  {test.replace(/([A-Z])/g, ' $1').trim()}
                </span>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(status)}
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(status)}`}>
                    {status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* PWA Install Prompt Test */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">PWA Install Prompt</h2>
          <p className="text-gray-600 mb-4">
            The PWA install prompt should appear automatically. Check the bottom-right corner or browser address bar for install options.
          </p>
          <PWAInstallPrompt />
        </div>

        {/* Push Notifications Test */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Push Notifications</h2>
          <PushNotifications />
        </div>

        {/* Swipe Gestures Test */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Swipe Gestures Test</h2>
          <p className="text-gray-600 mb-4">
            Try swiping left or right on the area below to test gesture recognition.
          </p>
          
          <SwipeGestures
            onSwipeLeft={() => alert('Swiped Left!')}
            onSwipeRight={() => alert('Swiped Right!')}
            onSwipeUp={() => alert('Swiped Up!')}
            onSwipeDown={() => alert('Swiped Down!')}
            className="bg-blue-50 border-2 border-dashed border-blue-300 rounded-lg p-8 text-center"
          >
            <div className="text-blue-600">
              <div className="text-4xl mb-2">👆</div>
              <p className="font-medium">Swipe in any direction</p>
              <p className="text-sm">Left, Right, Up, or Down</p>
            </div>
          </SwipeGestures>
        </div>

        {/* Pull to Refresh Test */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Pull to Refresh Test</h2>
          <p className="text-gray-600 mb-4">
            Pull down on the content below to test the pull-to-refresh functionality.
          </p>
          
          <PullToRefresh onRefresh={handleRefresh}>
            <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
              <div className="text-green-600">
                <div className="text-3xl mb-2">⬇️</div>
                <p className="font-medium">Pull down to refresh</p>
                <p className="text-sm">This content will refresh when pulled</p>
              </div>
            </div>
          </PullToRefresh>
        </div>

        {/* Camera Test */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Camera & Document Scanning</h2>
          <p className="text-gray-600 mb-4">
            Test the camera functionality and OCR document scanning.
          </p>
          
          <button
            onClick={() => setShowCamera(true)}
            className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg flex items-center space-x-2"
          >
            <Camera className="w-5 h-5" />
            <span>Open Camera Scanner</span>
          </button>

          {showCamera && (
            <CameraScanner
              onCapture={(data) => {
                console.log('Captured:', data);
                setShowCamera(false);
                alert('Document captured successfully!');
              }}
              onClose={() => setShowCamera(false)}
              autoExtractText={true}
            />
          )}
        </div>

        {/* Document Manager Test */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Document Management</h2>
          <DocumentManager />
        </div>

        {/* Maps Test */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">OpenStreetMap Integration</h2>
          <p className="text-gray-600 mb-4">
            Interactive map showing college locations with routing capabilities.
          </p>
          
          <OpenStreetMapIntegration
            colleges={mockColleges}
            showRoute={true}
            className="h-96"
          />
        </div>

        {/* Touch Navigation */}
        <TouchNavigation />
      </div>
    </div>
  );
}
